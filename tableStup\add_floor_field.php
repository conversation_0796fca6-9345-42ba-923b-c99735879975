<?php
/**
 * <PERSON><PERSON><PERSON> to add floor field to kp_tables table
 * This script will add a floor column and update all existing records
 */

session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

include '../dbconnect/_dbconnect.php';

try {
    // Connect to database
    $conn = db_connect();
    
    echo "<h2>Adding Floor Field to kp_tables Table</h2>";
    
    // Check if floor column already exists
    echo "<h3>Checking existing table structure...</h3>";
    $checkSql = "SHOW COLUMNS FROM kp_tables LIKE 'floor'";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->execute();
    $floorExists = $checkStmt->fetch();
    
    if ($floorExists) {
        echo "<p style='color: orange;'>⚠ Floor column already exists. Skipping column creation.</p>";
    } else {
        // Add floor column
        echo "<h3>Adding floor column...</h3>";
        $addColumnSql = "ALTER TABLE kp_tables ADD COLUMN floor INT NOT NULL DEFAULT 1 AFTER table_id";
        $addColumnStmt = $conn->prepare($addColumnSql);
        $addColumnStmt->execute();
        echo "<p style='color: green;'>✅ Successfully added floor column!</p>";
        
        // Add index for floor column
        echo "<h3>Adding index for floor column...</h3>";
        $addIndexSql = "ALTER TABLE kp_tables ADD INDEX idx_floor (floor)";
        $addIndexStmt = $conn->prepare($addIndexSql);
        $addIndexStmt->execute();
        echo "<p style='color: green;'>✅ Successfully added floor index!</p>";
    }
    
    // Update floor values based on table_id patterns
    echo "<h3>Updating floor values...</h3>";
    
    // Floor 1: VIP tables and C-rows
    $updateFloor1Sql = "UPDATE kp_tables SET floor = 1 WHERE table_id IN ('VIP1', 'VIP2') OR table_id LIKE 'C%'";
    $updateFloor1Stmt = $conn->prepare($updateFloor1Sql);
    $updateFloor1Stmt->execute();
    $floor1Count = $updateFloor1Stmt->rowCount();
    echo "<p style='color: green;'>✓ Updated $floor1Count Floor 1 tables</p>";
    
    // Floor 2: H-rows and B-rows
    $updateFloor2Sql = "UPDATE kp_tables SET floor = 2 WHERE table_id LIKE 'H%' OR table_id LIKE 'B%'";
    $updateFloor2Stmt = $conn->prepare($updateFloor2Sql);
    $updateFloor2Stmt->execute();
    $floor2Count = $updateFloor2Stmt->rowCount();
    echo "<p style='color: green;'>✓ Updated $floor2Count Floor 2 tables</p>";
    
    // Floor 3: A-rows
    $updateFloor3Sql = "UPDATE kp_tables SET floor = 3 WHERE table_id LIKE 'A%'";
    $updateFloor3Stmt = $conn->prepare($updateFloor3Sql);
    $updateFloor3Stmt->execute();
    $floor3Count = $updateFloor3Stmt->rowCount();
    echo "<p style='color: green;'>✓ Updated $floor3Count Floor 3 tables</p>";
    
    // Verify the updates
    echo "<h3>Verification</h3>";
    
    // Count tables by floor
    $verifyFloor1Sql = "SELECT COUNT(*) as count FROM kp_tables WHERE floor = 1";
    $verifyFloor1Stmt = $conn->prepare($verifyFloor1Sql);
    $verifyFloor1Stmt->execute();
    $verifyFloor1 = $verifyFloor1Stmt->fetch(PDO::FETCH_ASSOC);
    
    $verifyFloor2Sql = "SELECT COUNT(*) as count FROM kp_tables WHERE floor = 2";
    $verifyFloor2Stmt = $conn->prepare($verifyFloor2Sql);
    $verifyFloor2Stmt->execute();
    $verifyFloor2 = $verifyFloor2Stmt->fetch(PDO::FETCH_ASSOC);
    
    $verifyFloor3Sql = "SELECT COUNT(*) as count FROM kp_tables WHERE floor = 3";
    $verifyFloor3Stmt = $conn->prepare($verifyFloor3Sql);
    $verifyFloor3Stmt->execute();
    $verifyFloor3 = $verifyFloor3Stmt->fetch(PDO::FETCH_ASSOC);
    
    $totalSql = "SELECT COUNT(*) as count FROM kp_tables";
    $totalStmt = $conn->prepare($totalSql);
    $totalStmt->execute();
    $total = $totalStmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<ul>";
    echo "<li><strong>Floor 1 tables:</strong> " . $verifyFloor1['count'] . "</li>";
    echo "<li><strong>Floor 2 tables:</strong> " . $verifyFloor2['count'] . "</li>";
    echo "<li><strong>Floor 3 tables:</strong> " . $verifyFloor3['count'] . "</li>";
    echo "<li><strong>Total tables:</strong> " . $total['count'] . "</li>";
    echo "</ul>";
    
    // Show updated table structure
    echo "<h3>Updated Table Structure</h3>";
    $describeSql = "DESCRIBE kp_tables";
    $describeStmt = $conn->prepare($describeSql);
    $describeStmt->execute();
    $columns = $describeStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; background-color: white;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th>";
    echo "</tr>";
    
    foreach ($columns as $column) {
        $rowStyle = ($column['Field'] === 'floor') ? "style='background-color: #d4edda;'" : "";
        echo "<tr $rowStyle>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Show sample data
    echo "<h3>Sample Data with Floor Field</h3>";
    $sampleSql = "SELECT table_id, floor, status FROM kp_tables ORDER BY floor, table_id LIMIT 20";
    $sampleStmt = $conn->prepare($sampleSql);
    $sampleStmt->execute();
    $sampleData = $sampleStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; background-color: white;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th>Table ID</th><th>Floor</th><th>Status</th>";
    echo "</tr>";
    
    foreach ($sampleData as $row) {
        $floorColor = '';
        switch($row['floor']) {
            case 1: $floorColor = 'background-color: #e3f2fd;'; break;
            case 2: $floorColor = 'background-color: #f3e5f5;'; break;
            case 3: $floorColor = 'background-color: #e8f5e8;'; break;
        }
        echo "<tr style='$floorColor'>";
        echo "<td>" . htmlspecialchars($row['table_id']) . "</td>";
        echo "<td><strong>" . htmlspecialchars($row['floor']) . "</strong></td>";
        echo "<td>" . htmlspecialchars($row['status']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "<p><em>Showing first 20 records...</em></p>";
    
    $expectedTotal = $verifyFloor1['count'] + $verifyFloor2['count'] + $verifyFloor3['count'];
    if ($expectedTotal == $total['count'] && $total['count'] > 0) {
        echo "<p style='color: green; font-weight: bold; font-size: 18px;'>✅ Floor field successfully added and all tables updated!</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ Some tables may not have been updated correctly.</p>";
    }
    
    echo "<hr>";
    echo "<p><a href='index.php'>← Back to Table Setup</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error</h3>";
    echo "<p style='color: red;'>An error occurred: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.php'>← Back to Table Setup</a></p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Add Floor Field to kp_tables</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }
        h2, h3 { 
            color: #333; 
        }
        h2 {
            background-color: #007bff;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        h3 {
            background-color: #6c757d;
            color: white;
            padding: 10px;
            border-radius: 3px;
            margin-top: 20px;
        }
        ul, p {
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 10px 0;
        }
        table {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 10px 0;
            width: 100%;
        }
        a {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 10px;
        }
        a:hover {
            background-color: #0056b3;
        }
        hr {
            border: none;
            height: 2px;
            background-color: #dee2e6;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- Content is generated above -->
</body>
</html>
