# PDF Download Setup Guide

## Overview
The booking system now supports PDF downloads for floor reports. The system has been updated to use the mPDF library for generating actual PDF files instead of relying on browser printing.

## Current Status
- ✅ **Buttons Updated**: Changed from "Print to PDF" to "Download PDF" with download icons
- ✅ **JavaScript Updated**: Modified to trigger direct downloads instead of opening new windows
- ✅ **PHP Files Updated**: All export files now support mPDF with HTML fallback
- ⚠️ **mPDF Library**: Needs to be installed (see installation steps below)

## Installation Steps

### Option 1: Install mPDF via Composer (Recommended)
1. Open command prompt/terminal in the project root directory
2. Run: `composer install`
3. If you encounter SSL certificate issues, run:
   ```
   composer config -g -- disable-tls true
   composer config -g -- secure-http false
   composer install
   ```

### Option 2: Manual Installation
If Composer doesn't work, you can manually download mPDF:
1. Download mPDF from: https://github.com/mpdf/mpdf/releases
2. Extract to `vendor/mpdf/mpdf/` directory
3. Ensure the autoload.php file is accessible

## How It Works

### With mPDF Installed
- Generates actual PDF files
- Downloads directly to user's computer
- Professional PDF formatting
- Proper filename with date and floor information

### Without mPDF (Fallback)
- Downloads as HTML file
- Automatically opens print dialog
- User can save as PDF using browser's "Save as PDF" option
- Maintains all formatting and styling

## File Structure
```
booking/
├── export_pdf_1st_floor.php    # 1st floor PDF export
├── export_pdf_2nd_floor.php    # 2nd floor PDF export  
├── export_pdf_3rd_floor.php    # 3rd floor PDF export
├── export_pdf_all.php          # All floors PDF export
├── mainpage.php                # Updated buttons
├── footer.php                  # Updated JavaScript
└── PDF_SETUP_README.md         # This file
```

## Features
- **Smart Detection**: Automatically detects if mPDF is available
- **Graceful Fallback**: Falls back to HTML download if mPDF fails
- **Proper Filenames**: Uses descriptive filenames with date and floor info
- **Search Integration**: Includes search terms in filename when filtered
- **Error Handling**: Comprehensive error handling with user feedback

## Testing
1. Go to the booking page: http://localhost/SawasdeeBackend/booking/
2. Select a date with bookings
3. Click any of the "Download PDF" buttons (Fl. 1, Fl. 2, Fl. 3, All)
4. Verify that the file downloads properly

## Troubleshooting

### If PDF generation fails:
1. Check if `vendor/autoload.php` exists
2. Verify mPDF installation
3. Check PHP error logs
4. The system will automatically fall back to HTML download

### If downloads don't work:
1. Check browser popup blockers
2. Verify file permissions
3. Check server error logs

## Future Enhancements
- Add PDF password protection
- Include company logo in PDF header
- Add custom PDF templates
- Support for different paper sizes
- Email PDF functionality
