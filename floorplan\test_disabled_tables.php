<?php
/**
 * <PERSON>ript to test disabled table functionality
 * Adds sample disabled tables with different conditions
 */

session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

include '../dbconnect/_dbconnect.php';

try {
    $conn = db_connect();
    
    echo "<h2>Testing Disabled Table Functionality</h2>";
    echo "<p>This script will set up test scenarios for disabled tables.</p>";
    
    // Get today's date and tomorrow's date
    $today = date('Y-m-d');
    $tomorrow = date('Y-m-d', strtotime('+1 day'));
    $yesterday = date('Y-m-d', strtotime('-1 day'));
    
    echo "<h3>Setting up test scenarios...</h3>";
    
    // Test Scenario 1: Table disabled with no end date (permanently disabled)
    $updateSql1 = "UPDATE kp_tables SET status = 'disabled', disabled_until = NULL WHERE table_id = 'C1'";
    $stmt1 = $conn->prepare($updateSql1);
    $stmt1->execute();
    echo "<p style='color: orange;'>✓ C1: Set to disabled with no end date (permanently disabled)</p>";
    
    // Test Scenario 2: Table disabled until today (should be disabled today)
    $updateSql2 = "UPDATE kp_tables SET status = 'disabled', disabled_until = ? WHERE table_id = 'C2'";
    $stmt2 = $conn->prepare($updateSql2);
    $stmt2->execute([$today]);
    echo "<p style='color: orange;'>✓ C2: Set to disabled until today ({$today})</p>";
    
    // Test Scenario 3: Table disabled until tomorrow (should be disabled today)
    $updateSql3 = "UPDATE kp_tables SET status = 'disabled', disabled_until = ? WHERE table_id = 'C3'";
    $stmt3 = $conn->prepare($updateSql3);
    $stmt3->execute([$tomorrow]);
    echo "<p style='color: orange;'>✓ C3: Set to disabled until tomorrow ({$tomorrow})</p>";
    
    // Test Scenario 4: Table disabled until yesterday (should be active now)
    $updateSql4 = "UPDATE kp_tables SET status = 'disabled', disabled_until = ? WHERE table_id = 'C4'";
    $stmt4 = $conn->prepare($updateSql4);
    $stmt4->execute([$yesterday]);
    echo "<p style='color: blue;'>✓ C4: Set to disabled until yesterday ({$yesterday}) - should appear active</p>";
    
    // Test Scenario 5: Table active with no disabled_until (normal table)
    $updateSql5 = "UPDATE kp_tables SET status = 'active', disabled_until = NULL WHERE table_id = 'C5'";
    $stmt5 = $conn->prepare($updateSql5);
    $stmt5->execute();
    echo "<p style='color: green;'>✓ C5: Set to active with no disabled_until (normal table)</p>";
    
    // Test Scenario 6: Table active with disabled_until date (should be active - status overrides)
    $updateSql6 = "UPDATE kp_tables SET status = 'active', disabled_until = ? WHERE table_id = 'C6'";
    $stmt6 = $conn->prepare($updateSql6);
    $stmt6->execute([$today]);
    echo "<p style='color: green;'>✓ C6: Set to active with disabled_until today (should be active)</p>";
    
    // Show current test data
    echo "<h3>Current Test Data:</h3>";
    $testSql = "SELECT table_id, status, disabled_until FROM kp_tables WHERE table_id IN ('C1', 'C2', 'C3', 'C4', 'C5', 'C6') ORDER BY table_id";
    $testStmt = $conn->prepare($testSql);
    $testStmt->execute();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Table ID</th>";
    echo "<th style='padding: 8px;'>Status</th>";
    echo "<th style='padding: 8px;'>Disabled Until</th>";
    echo "<th style='padding: 8px;'>Expected Behavior Today ({$today})</th>";
    echo "</tr>";
    
    while ($row = $testStmt->fetch(PDO::FETCH_ASSOC)) {
        $expectedBehavior = '';
        $rowColor = '';
        
        if ($row['status'] === 'active' && empty($row['disabled_until'])) {
            $expectedBehavior = 'Normal (clickable)';
            $rowColor = 'background: #d4edda;'; // Green
        } elseif ($row['status'] === 'active' && !empty($row['disabled_until'])) {
            $expectedBehavior = 'Normal (clickable) - status overrides';
            $rowColor = 'background: #d4edda;'; // Green
        } elseif ($row['status'] === 'disabled' && empty($row['disabled_until'])) {
            $expectedBehavior = 'Disabled (not clickable)';
            $rowColor = 'background: #f8d7da;'; // Red
        } elseif ($row['status'] === 'disabled' && $row['disabled_until'] === $today) {
            $expectedBehavior = 'Disabled (not clickable)';
            $rowColor = 'background: #f8d7da;'; // Red
        } elseif ($row['status'] === 'disabled' && $row['disabled_until'] < $today) {
            $expectedBehavior = 'Should be active (past date)';
            $rowColor = 'background: #fff3cd;'; // Yellow
        } elseif ($row['status'] === 'disabled' && $row['disabled_until'] > $today) {
            $expectedBehavior = 'Disabled (future date)';
            $rowColor = 'background: #f8d7da;'; // Red
        }
        
        echo "<tr style='{$rowColor}'>";
        echo "<td style='padding: 8px;'><strong>" . htmlspecialchars($row['table_id']) . "</strong></td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($row['status']) . "</td>";
        echo "<td style='padding: 8px;'>" . ($row['disabled_until'] ? htmlspecialchars($row['disabled_until']) : '<em>NULL</em>') . "</td>";
        echo "<td style='padding: 8px;'>" . $expectedBehavior . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Testing Instructions:</h3>";
    echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<ol>";
    echo "<li><strong>Go to the test page:</strong> <a href='test_dynamic_tables.php' target='_blank'>test_dynamic_tables.php</a></li>";
    echo "<li><strong>Enable dynamic tables</strong> if not already enabled</li>";
    echo "<li><strong>Check Floor 1</strong> and look for tables C1-C6</li>";
    echo "<li><strong>Expected results:</strong>";
    echo "<ul>";
    echo "<li>C1: Should be grayed out (disabled permanently)</li>";
    echo "<li>C2: Should be grayed out (disabled until today)</li>";
    echo "<li>C3: Should be grayed out (disabled until tomorrow)</li>";
    echo "<li>C4: Should appear normal (disabled until yesterday - past date)</li>";
    echo "<li>C5: Should appear normal (active)</li>";
    echo "<li>C6: Should appear normal (active overrides disabled_until)</li>";
    echo "</ul></li>";
    echo "<li><strong>Try clicking:</strong> Disabled tables should show alert messages</li>";
    echo "<li><strong>Change date:</strong> Try changing the date in the date picker to see how it affects C2 and C3</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h3>Reset Test Data:</h3>";
    echo "<p>To reset all tables to normal status:</p>";
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<code>UPDATE kp_tables SET status = 'active', disabled_until = NULL WHERE table_id IN ('C1', 'C2', 'C3', 'C4', 'C5', 'C6');</code>";
    echo "</div>";
    
    echo "<div style='margin-top: 30px;'>";
    echo "<a href='test_dynamic_tables.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>→ Test Dynamic Tables</a>";
    echo "<a href='mainpage.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>→ Main Floorplan</a>";
    echo "<a href='admin_dynamic_tables.php' style='background: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>→ Admin Panel</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error</h3>";
    echo "<p style='color: red;'>An error occurred: " . $e->getMessage() . "</p>";
}
?>
