-- Create user_sessions table for online users tracking
-- This table tracks active user sessions to display online users

DROP TABLE IF EXISTS `user_sessions`;

CREATE TABLE `user_sessions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `user_id` INT NOT NULL,
    `session_id` VARCHAR(255) NOT NULL,
    `last_activity` TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `ip_address` VARCHAR(45),
    `user_agent` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_last_activity` (`last_activity`),
    UNIQUE KEY `unique_user_session` (`user_id`, `session_id`),
    FOREIGN KEY (`user_id`) REFERENCES `kp_login`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Tracks active user sessions for online users display';
