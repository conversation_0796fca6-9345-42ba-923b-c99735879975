<?php
/**
 * Update User Activity API
 * 
 * Updates the current user's last activity timestamp
 */

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Include database connection
require_once '../dbconnect/_dbconnect.php';

// Set content type to JSON
header('Content-Type: application/json');

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Connect to database
    $conn = db_connect();
    
    if (!$conn) {
        throw new Exception('Database connection failed');
    }
    
    // Get current user info
    $currentUserId = $_SESSION['id'];
    $sessionId = session_id();
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // Update session activity
    $updateSQL = "
        INSERT INTO user_sessions (user_id, session_id, ip_address, user_agent, last_activity) 
        VALUES (?, ?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE 
        last_activity = NOW(),
        ip_address = VALUES(ip_address),
        user_agent = VALUES(user_agent)
    ";
    
    $stmt = $conn->prepare($updateSQL);
    $result = $stmt->execute([$currentUserId, $sessionId, $ipAddress, $userAgent]);
    
    if ($result) {
        // Also update session last_activity
        $_SESSION['last_activity'] = time();
        
        echo json_encode([
            'success' => true,
            'message' => 'Activity updated successfully'
        ]);
    } else {
        throw new Exception('Failed to update activity');
    }
    
} catch (Exception $e) {
    // Log error
    error_log("Error in update_user_activity.php: " . $e->getMessage());
    
    // Return error response
    echo json_encode([
        'success' => false,
        'message' => 'Error updating activity: ' . $e->getMessage()
    ]);
} finally {
    // Close database connection
    if (isset($conn)) {
        db_close($conn);
    }
}
?>
