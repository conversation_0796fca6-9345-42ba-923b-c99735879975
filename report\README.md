# Sawasdee Backend - Reporting System

## Overview
The reporting system provides comprehensive analytics and data export capabilities for the booking system. It supports multiple report types with visual charts and export functionality.

## Report Types

### 1. Daily Report
- **Purpose**: Analyze bookings for a specific date
- **Parameters**: Single date selection
- **Data Includes**: All bookings, revenue, guest counts for the selected day
- **Use Case**: Daily operations review, daily revenue tracking

### 2. Monthly Report
- **Purpose**: Comprehensive monthly analysis
- **Parameters**: Month and year selection
- **Data Includes**: Monthly totals, daily breakdown within the month
- **Use Case**: Monthly performance review, trend analysis

### 3. Quarterly Report (3 Month)
- **Purpose**: Three-month period analysis
- **Parameters**: Start date (system calculates 3 months from this date)
- **Data Includes**: Quarterly totals, monthly breakdown within the period
- **Use Case**: Seasonal analysis, quarterly business reviews

### 4. Year to Date (YTD) Report
- **Purpose**: Current year performance from January 1st to present
- **Parameters**: Year selection
- **Data Includes**: YTD totals, monthly breakdown for the year
- **Use Case**: Annual performance tracking, budget vs actual analysis

## Features

### Visual Analytics
- **Summary Cards**: Key metrics at a glance (bookings, revenue, guests, average booking value)
- **Guest Distribution Chart**: Pie chart showing adults, children, and infants breakdown
- **Payment Status Chart**: Visual representation of payment statuses
- **Floor Distribution Chart**: Bar chart showing bookings and revenue by floor

### Data Export
- **Excel Export**: Detailed booking data in spreadsheet format
- **PDF Export**: Professional report format suitable for printing and sharing

### Real-time Updates
- Data refreshes automatically when parameters change
- Interactive charts with hover details
- Responsive design for mobile and desktop viewing

## How to Use

### Generating Reports
1. **Select Report Type**: Choose from Daily, Monthly, Quarterly, or YTD
2. **Set Parameters**: 
   - Daily: Select specific date
   - Monthly: Choose month and year
   - Quarterly: Select start date
   - YTD: Choose year
3. **Generate**: Click "Generate Report" button
4. **Review**: Analyze the summary cards, detailed table, and charts

### Exporting Data
1. **Generate Report**: First generate the report with desired parameters
2. **Choose Export Format**:
   - **Excel**: Click "Export Excel" for detailed data in spreadsheet format
   - **PDF**: Click "Export PDF" for formatted report suitable for sharing

### Understanding the Data

#### Summary Metrics
- **Total Bookings**: Number of confirmed bookings (excludes cancelled)
- **Total Revenue**: Sum of all booking amounts
- **Total Guests**: Combined count of adults, children, and infants
- **Average Booking Value**: Revenue divided by number of bookings

#### Detailed Breakdown
- **Adults/Children/Infants**: Guest type distribution with percentages
- **Guides/FOC/Team Leaders**: Staff allocation statistics
- **Floor Distribution**: Booking and revenue distribution across floors
- **Payment Status**: Paid vs Waiting Payment breakdown

## Technical Details

### Database Tables Used
- `kp_booking`: Main booking data
- `kp_agent`: Agent information for bookings

### API Endpoints
- `api/generate_report.php`: Main report generation
- `api/export_report_excel.php`: Excel export functionality
- `api/export_report_pdf.php`: PDF export functionality

### File Structure
```
report/
├── index.php              # Main entry point
├── mainpage.php           # Report interface
├── header.php             # HTML head and styles
├── footer.php             # JavaScript and scripts
├── test_api.php           # API testing interface
├── api/
│   ├── generate_report.php    # Report generation API
│   ├── export_report_excel.php # Excel export API
│   └── export_report_pdf.php   # PDF export API
└── README.md              # This documentation
```

## Troubleshooting

### Common Issues
1. **No Data Showing**: Check if there are bookings for the selected period
2. **Export Not Working**: Ensure proper permissions and PHP extensions are installed
3. **Charts Not Loading**: Verify Chart.js library is loaded correctly

### Requirements
- PHP 7.4 or higher
- MySQL/MariaDB database
- Modern web browser with JavaScript enabled
- For PDF export: mPDF library (optional, falls back to HTML print)

## Future Enhancements
- Agent-specific reports
- Comparison reports (period vs period)
- Automated report scheduling
- Email report delivery
- Advanced filtering options
- Custom date range selection
