<?php
/**
 * Generate Report API
 * 
 * Generates various types of reports: daily, monthly, quarterly, year-to-date
 */

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Include database connection
require_once '../../dbconnect/_dbconnect.php';

// Set content type to JSON
header('Content-Type: application/json');

// Accept both GET and POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET' && $_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get input data from both GET and POST
    $input = json_decode(file_get_contents('php://input'), true);

    // Support both GET and POST parameters
    $reportType = $input['type'] ?? $_GET['type'] ?? 'daterange';
    $startDate = $input['startDate'] ?? $_GET['from_date'] ?? date('Y-m-d');
    $endDate = $input['endDate'] ?? $_GET['to_date'] ?? date('Y-m-d');
    $month = $input['month'] ?? $_GET['month'] ?? date('Y-m');
    $year = $input['year'] ?? $_GET['year'] ?? date('Y');

    // Connect to database
    $conn = db_connect();

    if (!$conn) {
        throw new Exception('Database connection failed');
    }

    // Generate report based on type
    switch ($reportType) {
        case 'daily':
            $reportData = generateDailyReport($conn, $startDate);
            break;
        case 'monthly':
            $reportData = generateMonthlyReport($conn, $month);
            break;
        case 'quarterly':
            $reportData = generateQuarterlyReport($conn, $startDate);
            break;
        case 'ytd':
            $reportData = generateYearToDateReport($conn, $year);
            break;
        case 'daterange':
            $reportData = generateDateRangeReport($conn, $startDate, $endDate);
            break;
        default:
            throw new Exception('Invalid report type');
    }
    
    echo json_encode([
        'success' => true,
        'data' => $reportData,
        'type' => $reportType
    ]);
    
} catch (Exception $e) {
    error_log("Error in generate_report.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error generating report: ' . $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        db_close($conn);
    }
}

/**
 * Generate Daily Report
 */
function generateDailyReport($conn, $date) {
    // Get summary data (aggregated) - including all statuses
    $sql = "SELECT
                COUNT(*) as total_bookings,
                COALESCE(SUM(amount), 0) as total_amount,
                COALESCE(SUM(adult), 0) as total_adult,
                COALESCE(SUM(child), 0) as total_child,
                COALESCE(SUM(infant), 0) as total_infant,
                COALESCE(SUM(guide), 0) as total_guide,
                COALESCE(SUM(inspection), 0) as total_foc,
                COALESCE(SUM(team_leader), 0) as total_tl
            FROM kp_booking
            WHERE use_date = ?
            AND book_status != 'Cancel'";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$date]);
    $summary = $stmt->fetch(PDO::FETCH_ASSOC);

    // Get booking status summary (including cancelled and changed)
    $statusSql = "SELECT
                    book_status,
                    COUNT(*) as count,
                    COALESCE(SUM(adult + child + infant), 0) as total_people,
                    COALESCE(SUM(amount), 0) as total_amount
                  FROM kp_booking
                  WHERE use_date = ?
                  GROUP BY book_status";
    $stmt = $conn->prepare($statusSql);
    $stmt->execute([$date]);
    $statusData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get agent summary
    $agentSql = "SELECT
                    agent,
                    COUNT(*) as booking_count,
                    COALESCE(SUM(amount), 0) as total_amount,
                    COALESCE(SUM(adult + child + infant), 0) as total_people
                 FROM kp_booking
                 WHERE use_date = ? AND book_status != 'Cancel'
                 AND agent IS NOT NULL AND agent != ''
                 GROUP BY agent
                 ORDER BY total_amount DESC";
    $stmt = $conn->prepare($agentSql);
    $stmt->execute([$date]);
    $agentData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get user summary (who created the bookings)
    // Get user, name, summary from kp_login, kp_booking
    $userSql = "SELECT
                    kp_login.user as user_key,
                    kp_login.name as user_name,
                    COUNT(kp_booking.orderNo) as booking_count,
                    COALESCE(SUM(kp_booking.amount), 0) as total_amount,
                    COALESCE(SUM(kp_booking.adult + kp_booking.child + kp_booking.infant), 0) as total_people
                FROM kp_booking
                JOIN kp_login ON kp_booking.user_key = kp_login.user
                WHERE use_date = ? AND book_status != 'Cancel'
                AND kp_login.user IS NOT NULL AND kp_login.user != ''
                GROUP BY kp_login.user, kp_login.name
                ORDER BY booking_count DESC";

    /*
    $userSql = "SELECT
                    user_key,
                    COUNT(*) as booking_count,
                    COALESCE(SUM(amount), 0) as total_amount,
                    COALESCE(SUM(adult + child + infant), 0) as total_people
                FROM kp_booking
                WHERE use_date = ? AND book_status != 'Cancel'
                AND user_key IS NOT NULL AND user_key != ''
                GROUP BY user_key
                ORDER BY booking_count DESC";
    */
    $stmt = $conn->prepare($userSql);
    $stmt->execute([$date]);
    $userData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get floor distribution
    $floorSql = "SELECT use_zone, COUNT(*) as count, SUM(amount) as amount 
                 FROM kp_booking 
                 WHERE use_date = ? AND book_status != 'Cancel'
                 GROUP BY use_zone";
    $stmt = $conn->prepare($floorSql);
    $stmt->execute([$date]);
    $floorData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get payment status distribution
    $paymentSql = "SELECT payment_status, COUNT(*) as count 
                   FROM kp_booking 
                   WHERE use_date = ? AND book_status != 'Cancel'
                   GROUP BY payment_status";
    $stmt = $conn->prepare($paymentSql);
    $stmt->execute([$date]);
    $paymentData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get detailed bookings
    $detailSql = "SELECT * FROM kp_booking 
                  WHERE use_date = ? AND book_status != 'Cancel'
                  ORDER BY create_date DESC";
    $stmt = $conn->prepare($detailSql);
    $stmt->execute([$date]);
    $bookings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return [
        'summary' => $summary ?: getEmptySummary(),
        'floor_distribution' => $floorData,
        'payment_distribution' => $paymentData,
        'status_distribution' => $statusData,
        'agent_summary' => $agentData,
        'user_summary' => $userData,
        'bookings' => $bookings,
        'period' => $date,
        'period_label' => date('F j, Y', strtotime($date))
    ];
}

/**
 * Generate Monthly Report
 */
function generateMonthlyReport($conn, $month) {
    $year = substr($month, 0, 4);
    $monthNum = substr($month, 5, 2);
    
    $sql = "SELECT 
                COUNT(*) as total_bookings,
                COALESCE(SUM(amount), 0) as total_amount,
                COALESCE(SUM(adult), 0) as total_adult,
                COALESCE(SUM(child), 0) as total_child,
                COALESCE(SUM(infant), 0) as total_infant,
                COALESCE(SUM(guide), 0) as total_guide,
                COALESCE(SUM(inspection), 0) as total_foc,
                COALESCE(SUM(team_leader), 0) as total_tl
            FROM kp_booking 
            WHERE YEAR(use_date) = ? AND MONTH(use_date) = ?
            AND book_status != 'Cancel'";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$year, $monthNum]);
    $summary = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get daily breakdown for the month
    $dailySql = "SELECT
                    DATE(use_date) as date,
                    COUNT(*) as bookings,
                    SUM(amount) as amount
                 FROM kp_booking
                 WHERE YEAR(use_date) = ? AND MONTH(use_date) = ?
                 AND book_status != 'Cancel'
                 GROUP BY DATE(use_date)
                 ORDER BY DATE(use_date)";
    $stmt = $conn->prepare($dailySql);
    $stmt->execute([$year, $monthNum]);
    $dailyData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get floor distribution
    $floorSql = "SELECT use_zone, COUNT(*) as count, SUM(amount) as amount
                 FROM kp_booking
                 WHERE YEAR(use_date) = ? AND MONTH(use_date) = ?
                 AND book_status != 'Cancel'
                 GROUP BY use_zone";
    $stmt = $conn->prepare($floorSql);
    $stmt->execute([$year, $monthNum]);
    $floorData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get booking status summary
    $statusSql = "SELECT
                    book_status,
                    COUNT(*) as count,
                    COALESCE(SUM(adult + child + infant), 0) as total_people,
                    COALESCE(SUM(amount), 0) as total_amount
                  FROM kp_booking
                  WHERE YEAR(use_date) = ? AND MONTH(use_date) = ?
                  GROUP BY book_status";
    $stmt = $conn->prepare($statusSql);
    $stmt->execute([$year, $monthNum]);
    $statusData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get agent summary
    $agentSql = "SELECT
                    agent,
                    COUNT(*) as booking_count,
                    COALESCE(SUM(amount), 0) as total_amount,
                    COALESCE(SUM(adult + child + infant), 0) as total_people
                 FROM kp_booking
                 WHERE YEAR(use_date) = ? AND MONTH(use_date) = ?
                 AND book_status != 'Cancel'
                 AND agent IS NOT NULL AND agent != ''
                 GROUP BY agent
                 ORDER BY total_amount DESC";
    $stmt = $conn->prepare($agentSql);
    $stmt->execute([$year, $monthNum]);
    $agentData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get user, name, summary from kp_login, kp_booking
    /*
    $userSql = "SELECT
                    kp_login.user as user_key,
                    kp_login.name as user_name,
                    COUNT(kp_booking.orderNo) as booking_count,
                    COALESCE(SUM(kp_booking.amount), 0) as total_amount,
                    COALESCE(SUM(kp_booking.adult + kp_booking.child + kp_booking.infant), 0) as total_people
                FROM kp_booking
                JOIN kp_login ON kp_booking.user_key = kp_login.user
                WHERE YEAR(use_date) = ? AND MONTH(use_date) = ?
                AND book_status != 'Cancel'
                AND kp_login.user IS NOT NULL AND kp_login.user != ''
                GROUP BY kp_login.user, kp_login.name
                ORDER BY booking_count DESC";
    */

    $userSql = "SELECT
                    user_key,
                    COUNT(*) as booking_count,
                    COALESCE(SUM(amount), 0) as total_amount,
                    COALESCE(SUM(adult + child + infant), 0) as total_people
                FROM kp_booking
                WHERE YEAR(use_date) = ? AND MONTH(use_date) = ?
                AND book_status != 'Cancel'
                AND user_key IS NOT NULL AND user_key != ''
                GROUP BY user_key
                ORDER BY booking_count DESC";
    $stmt = $conn->prepare($userSql);
    $stmt->execute([$year, $monthNum]);
    $userData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return [
        'summary' => $summary ?: getEmptySummary(),
        'daily_breakdown' => $dailyData,
        'floor_distribution' => $floorData,
        'status_distribution' => $statusData,
        'agent_summary' => $agentData,
        'user_summary' => $userData,
        'period' => $month,
        'period_label' => date('F Y', strtotime($month . '-01'))
    ];
}

/**
 * Generate Quarterly Report (3 months)
 */
function generateQuarterlyReport($conn, $startDate) {
    $endDate = date('Y-m-d', strtotime($startDate . ' +3 months'));
    
    $sql = "SELECT 
                COUNT(*) as total_bookings,
                COALESCE(SUM(amount), 0) as total_amount,
                COALESCE(SUM(adult), 0) as total_adult,
                COALESCE(SUM(child), 0) as total_child,
                COALESCE(SUM(infant), 0) as total_infant,
                COALESCE(SUM(guide), 0) as total_guide,
                COALESCE(SUM(inspection), 0) as total_foc,
                COALESCE(SUM(team_leader), 0) as total_tl
            FROM kp_booking 
            WHERE use_date >= ? AND use_date < ?
            AND book_status != 'Cancel'";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$startDate, $endDate]);
    $summary = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get monthly breakdown
    $monthlySql = "SELECT
                      YEAR(use_date) as year,
                      MONTH(use_date) as month,
                      COUNT(*) as bookings,
                      SUM(amount) as amount
                   FROM kp_booking
                   WHERE use_date >= ? AND use_date < ?
                   AND book_status != 'Cancel'
                   GROUP BY YEAR(use_date), MONTH(use_date)
                   ORDER BY year, month";
    $stmt = $conn->prepare($monthlySql);
    $stmt->execute([$startDate, $endDate]);
    $monthlyData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get booking status summary
    $statusSql = "SELECT
                    book_status,
                    COUNT(*) as count,
                    COALESCE(SUM(adult + child + infant), 0) as total_people,
                    COALESCE(SUM(amount), 0) as total_amount
                  FROM kp_booking
                  WHERE use_date >= ? AND use_date < ?
                  GROUP BY book_status";
    $stmt = $conn->prepare($statusSql);
    $stmt->execute([$startDate, $endDate]);
    $statusData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get agent summary
    $agentSql = "SELECT
                    agent,
                    COUNT(*) as booking_count,
                    COALESCE(SUM(amount), 0) as total_amount,
                    COALESCE(SUM(adult + child + infant), 0) as total_people
                 FROM kp_booking
                 WHERE use_date >= ? AND use_date < ?
                 AND book_status != 'Cancel'
                 AND agent IS NOT NULL AND agent != ''
                 GROUP BY agent
                 ORDER BY total_amount DESC";
    $stmt = $conn->prepare($agentSql);
    $stmt->execute([$startDate, $endDate]);
    $agentData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get user summary
    // Get user, name, summary from kp_login, kp_booking
    /*
    $userSql = "SELECT
                    kp_login.user as user_key,
                    kp_login.name as user_name,
                    COUNT(kp_booking.orderNo) as booking_count,
                    COALESCE(SUM(kp_booking.amount), 0) as total_amount,
                    COALESCE(SUM(kp_booking.adult + kp_booking.child + kp_booking.infant), 0) as total_people
                FROM kp_booking
                JOIN kp_login ON kp_booking.user_key = kp_login.user
                WHERE use_date >= ? AND use_date < ?
                AND book_status != 'Cancel'
                AND kp_login.user IS NOT NULL AND kp_login.user != ''
                GROUP BY kp_login.user, kp_login.name
                ORDER BY booking_count DESC";
    */

    $userSql = "SELECT
                    user_key,
                    COUNT(*) as booking_count,
                    COALESCE(SUM(amount), 0) as total_amount,
                    COALESCE(SUM(adult + child + infant), 0) as total_people
                FROM kp_booking
                WHERE use_date >= ? AND use_date < ?
                AND book_status != 'Cancel'
                AND user_key IS NOT NULL AND user_key != ''
                GROUP BY user_key
                ORDER BY booking_count DESC";

$stmt = $conn->prepare($userSql);
    $stmt->execute([$startDate, $endDate]);
    $userData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return [
        'summary' => $summary ?: getEmptySummary(),
        'monthly_breakdown' => $monthlyData,
        'status_distribution' => $statusData,
        'agent_summary' => $agentData,
        'user_summary' => $userData,
        'period' => $startDate . ' to ' . $endDate,
        'period_label' => date('M j, Y', strtotime($startDate)) . ' - ' . date('M j, Y', strtotime($endDate))
    ];
}

/**
 * Generate Year to Date Report
 */
function generateYearToDateReport($conn, $year) {
    $startDate = $year . '-01-01';
    $endDate = date('Y-m-d');
    
    $sql = "SELECT 
                COUNT(*) as total_bookings,
                COALESCE(SUM(amount), 0) as total_amount,
                COALESCE(SUM(adult), 0) as total_adult,
                COALESCE(SUM(child), 0) as total_child,
                COALESCE(SUM(infant), 0) as total_infant,
                COALESCE(SUM(guide), 0) as total_guide,
                COALESCE(SUM(inspection), 0) as total_foc,
                COALESCE(SUM(team_leader), 0) as total_tl
            FROM kp_booking 
            WHERE YEAR(use_date) = ?
            AND book_status != 'Cancel'";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$year]);
    $summary = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get monthly breakdown for the year
    $monthlySql = "SELECT
                      MONTH(use_date) as month,
                      COUNT(*) as bookings,
                      SUM(amount) as amount
                   FROM kp_booking
                   WHERE YEAR(use_date) = ?
                   AND book_status != 'Cancel'
                   GROUP BY MONTH(use_date)
                   ORDER BY month";
    $stmt = $conn->prepare($monthlySql);
    $stmt->execute([$year]);
    $monthlyData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get booking status summary
    $statusSql = "SELECT
                    book_status,
                    COUNT(*) as count,
                    COALESCE(SUM(adult + child + infant), 0) as total_people,
                    COALESCE(SUM(amount), 0) as total_amount
                  FROM kp_booking
                  WHERE YEAR(use_date) = ?
                  GROUP BY book_status";
    $stmt = $conn->prepare($statusSql);
    $stmt->execute([$year]);
    $statusData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get agent summary
    $agentSql = "SELECT
                    agent,
                    COUNT(*) as booking_count,
                    COALESCE(SUM(amount), 0) as total_amount,
                    COALESCE(SUM(adult + child + infant), 0) as total_people
                 FROM kp_booking
                 WHERE YEAR(use_date) = ?
                 AND book_status != 'Cancel'
                 AND agent IS NOT NULL AND agent != ''
                 GROUP BY agent
                 ORDER BY total_amount DESC";
    $stmt = $conn->prepare($agentSql);
    $stmt->execute([$year]);
    $agentData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get user summary
    // Get user, name, summary from kp_login, kp_booking
    /*
    $userSql = "SELECT
                    kp_login.user as user_key,
                    kp_login.name as user_name,
                    COUNT(kp_booking.orderNo) as booking_count,
                    COALESCE(SUM(kp_booking.amount), 0) as total_amount,
                    COALESCE(SUM(kp_booking.adult + kp_booking.child + kp_booking.infant), 0) as total_people
                FROM kp_booking
                JOIN kp_login ON kp_booking.user_key = kp_login.user
                WHERE YEAR(use_date) = ?
                AND book_status != 'Cancel'
                AND kp_login.user IS NOT NULL AND kp_login.user != ''
                GROUP BY kp_login.user, kp_login.name
                ORDER BY booking_count DESC";
    */

    $userSql = "SELECT
                    user_key,
                    COUNT(*) as booking_count,
                    COALESCE(SUM(amount), 0) as total_amount,
                    COALESCE(SUM(adult + child + infant), 0) as total_people
                FROM kp_booking
                WHERE YEAR(use_date) = ?
                AND book_status != 'Cancel'
                AND user_key IS NOT NULL AND user_key != ''
                GROUP BY user_key
                ORDER BY booking_count DESC";

    $stmt = $conn->prepare($userSql);
    $stmt->execute([$year]);
    $userData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return [
        'summary' => $summary ?: getEmptySummary(),
        'monthly_breakdown' => $monthlyData,
        'status_distribution' => $statusData,
        'agent_summary' => $agentData,
        'user_summary' => $userData,
        'period' => $year,
        'period_label' => 'Year ' . $year . ' (Year to Date)'
    ];
}

/**
 * Generate Date Range Report
 */
function generateDateRangeReport($conn, $startDate, $endDate) {
    // Get summary data (aggregated)
    $sql = "SELECT
                COUNT(*) as total_bookings,
                COALESCE(SUM(amount), 0) as total_amount,
                COALESCE(SUM(adult), 0) as total_adult,
                COALESCE(SUM(child), 0) as total_child,
                COALESCE(SUM(infant), 0) as total_infant,
                COALESCE(SUM(guide), 0) as total_guide,
                COALESCE(SUM(inspection), 0) as total_foc,
                COALESCE(SUM(team_leader), 0) as total_tl
            FROM kp_booking
            WHERE use_date >= ? AND use_date <= ?
            AND book_status != 'Cancel'";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$startDate, $endDate]);
    $summary = $stmt->fetch(PDO::FETCH_ASSOC);

    // Get daily breakdown for the date range
    $dailySql = "SELECT
                    DATE(use_date) as date,
                    COUNT(*) as bookings,
                    SUM(amount) as amount
                 FROM kp_booking
                 WHERE use_date >= ? AND use_date <= ?
                 AND book_status != 'Cancel'
                 GROUP BY DATE(use_date)
                 ORDER BY DATE(use_date)";
    $stmt = $conn->prepare($dailySql);
    $stmt->execute([$startDate, $endDate]);
    $dailyData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get floor distribution
    $floorSql = "SELECT use_zone, COUNT(*) as count, SUM(amount) as amount
                 FROM kp_booking
                 WHERE use_date >= ? AND use_date <= ?
                 AND book_status != 'Cancel'
                 GROUP BY use_zone";
    $stmt = $conn->prepare($floorSql);
    $stmt->execute([$startDate, $endDate]);
    $floorData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get payment status distribution
    $paymentSql = "SELECT payment_status, COUNT(*) as count
                   FROM kp_booking
                   WHERE use_date >= ? AND use_date <= ?
                   AND book_status != 'Cancel'
                   GROUP BY payment_status";
    $stmt = $conn->prepare($paymentSql);
    $stmt->execute([$startDate, $endDate]);
    $paymentData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get booking status summary
    $statusSql = "SELECT
                    book_status,
                    COUNT(*) as count,
                    COALESCE(SUM(adult + child + infant), 0) as total_people,
                    COALESCE(SUM(amount), 0) as total_amount
                  FROM kp_booking
                  WHERE use_date >= ? AND use_date <= ?
                  GROUP BY book_status";
    $stmt = $conn->prepare($statusSql);
    $stmt->execute([$startDate, $endDate]);
    $statusData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get agent summary
    $agentSql = "SELECT
                    agent,
                    COUNT(*) as booking_count,
                    COALESCE(SUM(amount), 0) as total_amount,
                    COALESCE(SUM(adult + child + infant), 0) as total_people
                 FROM kp_booking
                 WHERE use_date >= ? AND use_date <= ?
                 AND book_status != 'Cancel'
                 AND agent IS NOT NULL AND agent != ''
                 GROUP BY agent
                 ORDER BY total_amount DESC";
    $stmt = $conn->prepare($agentSql);
    $stmt->execute([$startDate, $endDate]);
    $agentData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get user summary
    // Get user, name, summary from kp_login, kp_booking
    /*
    $userSql = "SELECT
                    kp_login.user as user_key,
                    kp_login.name as user_name,
                    COUNT(kp_booking.orderNo) as booking_count,
                    COALESCE(SUM(kp_booking.amount), 0) as total_amount,
                    COALESCE(SUM(kp_booking.adult + kp_booking.child + kp_booking.infant), 0) as total_people
                FROM kp_booking
                JOIN kp_login ON kp_booking.user_key = kp_login.user
                WHERE use_date >= ? AND use_date <= ?
                AND book_status != 'Cancel'
                AND kp_login.user IS NOT NULL AND kp_login.user != ''
                GROUP BY kp_login.user, kp_login.name
                ORDER BY booking_count DESC";
    */

    $userSql = "SELECT
                    user_key,
                    COUNT(*) as booking_count,
                    COALESCE(SUM(amount), 0) as total_amount,
                    COALESCE(SUM(adult + child + infant), 0) as total_people
                FROM kp_booking
                WHERE use_date >= ? AND use_date <= ?
                AND book_status != 'Cancel'
                AND user_key IS NOT NULL AND user_key != ''
                GROUP BY user_key
                ORDER BY booking_count DESC";

    $stmt = $conn->prepare($userSql);
    $stmt->execute([$startDate, $endDate]);
    $userData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return [
        'summary' => $summary ?: getEmptySummary(),
        'daily_breakdown' => $dailyData,
        'floor_distribution' => $floorData,
        'payment_distribution' => $paymentData,
        'status_distribution' => $statusData,
        'agent_summary' => $agentData,
        'user_summary' => $userData,
        'period' => $startDate . ' to ' . $endDate,
        'period_label' => date('M j, Y', strtotime($startDate)) . ' - ' . date('M j, Y', strtotime($endDate))
    ];
}

/**
 * Get empty summary structure
 */
function getEmptySummary() {
    return [
        'total_bookings' => 0,
        'total_amount' => 0,
        'total_adult' => 0,
        'total_child' => 0,
        'total_infant' => 0,
        'total_guide' => 0,
        'total_foc' => 0,
        'total_tl' => 0
    ];
}
?>
