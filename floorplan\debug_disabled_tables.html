<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Disabled Tables</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        button { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #005a87; }
        .log { background: #f9f9f9; border-left: 4px solid #007cba; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Debug Disabled Tables Functionality</h1>
    
    <div class="test-section">
        <h2>1. Test API Response</h2>
        <button onclick="testAPIResponse()">Test get_disabled_tables.php</button>
        <div id="api-results"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Test JavaScript Processing</h2>
        <button onclick="testJavaScriptProcessing()">Test Response Processing</button>
        <div id="js-results"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Test Table Filtering</h2>
        <button onclick="testTableFiltering()">Test Zone Filtering</button>
        <div id="filter-results"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Console Logs</h2>
        <div id="console-logs" class="log">
            <p>Open browser console (F12) to see detailed logs</p>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Test 1: API Response
        function testAPIResponse() {
            console.log('=== Testing API Response ===');
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<p>Testing API...</p>';
            
            const testParams = [
                { useZone: '1', useDate: '2025-07-26' },
                { useZone: '2', useDate: '2025-07-26' },
                { useZone: '3', useDate: '2025-07-26' }
            ];
            
            let results = '<h3>API Test Results:</h3>';
            let testCount = 0;
            
            testParams.forEach((params, index) => {
                $.ajax({
                    url: 'get_disabled_tables.php',
                    type: 'GET',
                    data: params,
                    dataType: 'json',
                    success: function(response) {
                        testCount++;
                        console.log(`✅ Zone ${params.useZone} API Response:`, response);
                        
                        results += `<div class="success">
                            <h4>Zone ${params.useZone}:</h4>
                            <p>Status: ${response.status}</p>
                            <p>Count: ${response.count}</p>
                            <p>Tables: ${response.tables ? response.tables.length : 0}</p>
                        </div>`;
                        
                        if (response.tables && response.tables.length > 0) {
                            results += '<ul>';
                            response.tables.forEach(table => {
                                results += `<li>Table ${table.id} (Floor ${table.floor}) - Until: ${table.disabled_until || 'Permanent'}</li>`;
                            });
                            results += '</ul>';
                        }
                        
                        if (testCount === testParams.length) {
                            resultsDiv.innerHTML = results;
                        }
                    },
                    error: function(xhr, status, error) {
                        testCount++;
                        console.error(`❌ Zone ${params.useZone} API Error:`, error);
                        
                        results += `<div class="error">
                            <h4>Zone ${params.useZone} - ERROR:</h4>
                            <p>Status: ${status}</p>
                            <p>Error: ${error}</p>
                            <p>Response: ${xhr.responseText}</p>
                        </div>`;
                        
                        if (testCount === testParams.length) {
                            resultsDiv.innerHTML = results;
                        }
                    }
                });
            });
        }
        
        // Test 2: JavaScript Processing
        function testJavaScriptProcessing() {
            console.log('=== Testing JavaScript Processing ===');
            const resultsDiv = document.getElementById('js-results');
            
            // Simulate the handleDisabledTablesSuccess function
            const mockResponses = [
                // New format
                {
                    status: 'success',
                    selected_date: '2025-07-26',
                    selected_floor: '1',
                    count: 2,
                    tables: [
                        { id: 'C12', status: 'disabled', floor: '1', disabled_until: '2025-08-01' },
                        { id: 'C14', status: 'disabled', floor: '1', disabled_until: null }
                    ]
                },
                // Old format (array)
                [
                    { id: 'C15', status: 'disabled', floor: '1', disabled_until: '2025-08-01' }
                ],
                // Invalid format
                { invalid: 'data' }
            ];
            
            let results = '<h3>JavaScript Processing Results:</h3>';
            
            mockResponses.forEach((response, index) => {
                console.log(`Testing response format ${index + 1}:`, response);
                
                let disabled = [];
                let formatType = 'Unknown';
                
                // Simulate the processing logic
                if (response && response.status === 'success' && Array.isArray(response.tables)) {
                    disabled = response.tables;
                    formatType = 'New format (object with tables array)';
                } else if (Array.isArray(response)) {
                    disabled = response;
                    formatType = 'Old format (direct array)';
                } else {
                    disabled = [];
                    formatType = 'Invalid format';
                }
                
                results += `<div class="${disabled.length > 0 ? 'success' : 'warning'}">
                    <h4>Test ${index + 1}: ${formatType}</h4>
                    <p>Processed tables: ${disabled.length}</p>
                    <pre>${JSON.stringify(disabled, null, 2)}</pre>
                </div>`;
            });
            
            resultsDiv.innerHTML = results;
        }
        
        // Test 3: Table Filtering
        function testTableFiltering() {
            console.log('=== Testing Table Filtering ===');
            const resultsDiv = document.getElementById('filter-results');
            
            // Mock disabled tables data
            const mockTables = [
                { id: 'C12', status: 'disabled', floor: '1' },
                { id: 'C14', status: 'disabled', floor: '1' },
                { id: 'H1', status: 'disabled', floor: '2' },
                { id: 'B5', status: 'disabled', floor: '2' },
                { id: 'A1', status: 'disabled', floor: '3' },
                { id: 'invalid', status: 'disabled', floor: '1' }
            ];
            
            // Simulate the filtering logic
            const zoneFilters = {
                1: (table) => {
                    if (!table || !table.id) return false;
                    const id = table.id.toString().toUpperCase();
                    return id.startsWith('C');
                },
                2: (table) => {
                    if (!table || !table.id) return false;
                    const id = table.id.toString().toUpperCase();
                    return id.startsWith('H') || id.startsWith('B');
                },
                3: (table) => {
                    if (!table || !table.id) return false;
                    const id = table.id.toString().toUpperCase();
                    return id.startsWith('A');
                }
            };
            
            let results = '<h3>Table Filtering Results:</h3>';
            results += `<p>Total mock tables: ${mockTables.length}</p>`;
            
            [1, 2, 3].forEach(zone => {
                const filtered = mockTables.filter(zoneFilters[zone] || (() => false));
                console.log(`Zone ${zone} filtered tables:`, filtered);
                
                results += `<div class="info">
                    <h4>Zone ${zone}:</h4>
                    <p>Filtered tables: ${filtered.length}</p>
                    <ul>`;
                
                filtered.forEach(table => {
                    results += `<li>Table ${table.id} (Floor ${table.floor})</li>`;
                });
                
                results += '</ul></div>';
            });
            
            resultsDiv.innerHTML = results;
        }
        
        // Auto-run tests on page load
        window.onload = function() {
            console.log('Debug page loaded. Click buttons to run tests.');
            
            // Add console log capture
            const originalLog = console.log;
            const originalError = console.error;
            const originalWarn = console.warn;
            
            const logDiv = document.getElementById('console-logs');
            
            function addLogEntry(type, args) {
                const timestamp = new Date().toLocaleTimeString();
                const message = Array.from(args).map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                ).join(' ');
                
                const logEntry = document.createElement('div');
                logEntry.className = type;
                logEntry.innerHTML = `<strong>[${timestamp}] ${type.toUpperCase()}:</strong> ${message}`;
                logDiv.appendChild(logEntry);
                logDiv.scrollTop = logDiv.scrollHeight;
            }
            
            console.log = function(...args) {
                originalLog.apply(console, args);
                addLogEntry('info', args);
            };
            
            console.error = function(...args) {
                originalError.apply(console, args);
                addLogEntry('error', args);
            };
            
            console.warn = function(...args) {
                originalWarn.apply(console, args);
                addLogEntry('warning', args);
            };
            
            console.log('Console logging initialized');
        };
    </script>
</body>
</html>
