<?php
/**
 * Export Report to Excel
 * 
 * Exports report data to Excel format
 */

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Location: ../../login/index.php');
    exit;
}

// Include database connection
require_once '../../dbconnect/_dbconnect.php';

try {
    // Get parameters
    $reportType = $_GET['type'] ?? 'daily';
    $startDate = $_GET['startDate'] ?? date('Y-m-d');
    $endDate = $_GET['endDate'] ?? date('Y-m-d');
    $month = $_GET['month'] ?? date('Y-m');
    $year = $_GET['year'] ?? date('Y');
    
    // Connect to database
    $conn = db_connect();
    
    if (!$conn) {
        throw new Exception('Database connection failed');
    }
    
    // Get report data based on type
    $reportData = generateReportData($conn, $reportType, $startDate, $endDate, $month, $year);
    
    // Set headers for Excel download
    $filename = "Report_" . ucfirst($reportType) . "_" . date('Y-m-d') . ".xls";
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    // Generate Excel content
    echo generateExcelContent($reportData, $reportType);
    
} catch (Exception $e) {
    error_log("Error in export_report_excel.php: " . $e->getMessage());
    echo "Error generating Excel report: " . $e->getMessage();
} finally {
    if (isset($conn)) {
        db_close($conn);
    }
}

function generateReportData($conn, $reportType, $startDate, $endDate, $month, $year) {
    switch ($reportType) {
        case 'daily':
            return getDailyReportData($conn, $startDate);
        case 'monthly':
            return getMonthlyReportData($conn, $month);
        case 'quarterly':
            return getQuarterlyReportData($conn, $startDate);
        case 'ytd':
            return getYearToDateReportData($conn, $year);
        default:
            throw new Exception('Invalid report type');
    }
}

function getDailyReportData($conn, $date) {
    $sql = "SELECT 
                orderNo, name, phone, adult, child, infant, guide, inspection as foc, team_leader as tl,
                use_date, use_zone, voucher, agent, remark, amount, payment_status, pay_type,
                book_status, create_date
            FROM kp_booking 
            WHERE use_date = ? 
            AND book_status != 'Cancel'
            ORDER BY create_date DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$date]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getMonthlyReportData($conn, $month) {
    $year = substr($month, 0, 4);
    $monthNum = substr($month, 5, 2);
    
    $sql = "SELECT 
                orderNo, name, phone, adult, child, infant, guide, inspection as foc, team_leader as tl,
                use_date, use_zone, voucher, agent, remark, amount, payment_status, pay_type,
                book_status, create_date
            FROM kp_booking 
            WHERE YEAR(use_date) = ? AND MONTH(use_date) = ?
            AND book_status != 'Cancel'
            ORDER BY use_date DESC, create_date DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$year, $monthNum]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getQuarterlyReportData($conn, $startDate) {
    $endDate = date('Y-m-d', strtotime($startDate . ' +3 months'));
    
    $sql = "SELECT 
                orderNo, name, phone, adult, child, infant, guide, inspection as foc, team_leader as tl,
                use_date, use_zone, voucher, agent, remark, amount, payment_status, pay_type,
                book_status, create_date
            FROM kp_booking 
            WHERE use_date >= ? AND use_date < ?
            AND book_status != 'Cancel'
            ORDER BY use_date DESC, create_date DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$startDate, $endDate]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getYearToDateReportData($conn, $year) {
    $sql = "SELECT 
                orderNo, name, phone, adult, child, infant, guide, inspection as foc, team_leader as tl,
                use_date, use_zone, voucher, agent, remark, amount, payment_status, pay_type,
                book_status, create_date
            FROM kp_booking 
            WHERE YEAR(use_date) = ?
            AND book_status != 'Cancel'
            ORDER BY use_date DESC, create_date DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$year]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function generateExcelContent($data, $reportType) {
    $html = '<html>';
    $html .= '<head>';
    $html .= '<meta charset="UTF-8">';
    $html .= '<style>';
    $html .= 'table { border-collapse: collapse; width: 100%; }';
    $html .= 'th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }';
    $html .= 'th { background-color: #f2f2f2; font-weight: bold; }';
    $html .= '</style>';
    $html .= '</head>';
    $html .= '<body>';
    
    $html .= '<h2>' . ucfirst($reportType) . ' Report - Generated on ' . date('Y-m-d H:i:s') . '</h2>';
    
    if (empty($data)) {
        $html .= '<p>No data found for the selected period.</p>';
    } else {
        $html .= '<table>';
        $html .= '<thead>';
        $html .= '<tr>';
        $html .= '<th>Order No</th>';
        $html .= '<th>Customer Name</th>';
        $html .= '<th>Phone</th>';
        $html .= '<th>Use Date</th>';
        $html .= '<th>Floor</th>';
        $html .= '<th>Adult</th>';
        $html .= '<th>Child</th>';
        $html .= '<th>Infant</th>';
        $html .= '<th>Guide</th>';
        $html .= '<th>FOC</th>';
        $html .= '<th>T/L</th>';
        $html .= '<th>Amount</th>';
        $html .= '<th>Payment Status</th>';
        $html .= '<th>Payment Type</th>';
        $html .= '<th>Booking Status</th>';
        $html .= '<th>Agent</th>';
        $html .= '<th>Voucher</th>';
        $html .= '<th>Remark</th>';
        $html .= '<th>Create Date</th>';
        $html .= '</tr>';
        $html .= '</thead>';
        $html .= '<tbody>';
        
        foreach ($data as $row) {
            $html .= '<tr>';
            $html .= '<td>' . htmlspecialchars($row['orderNo'] ?? '') . '</td>';
            $html .= '<td>' . htmlspecialchars($row['name'] ?? '') . '</td>';
            $html .= '<td>' . htmlspecialchars($row['phone'] ?? '') . '</td>';
            $html .= '<td>' . htmlspecialchars($row['use_date'] ?? '') . '</td>';
            $html .= '<td>' . htmlspecialchars($row['use_zone'] ?? '') . '</td>';
            $html .= '<td>' . htmlspecialchars($row['adult'] ?? '0') . '</td>';
            $html .= '<td>' . htmlspecialchars($row['child'] ?? '0') . '</td>';
            $html .= '<td>' . htmlspecialchars($row['infant'] ?? '0') . '</td>';
            $html .= '<td>' . htmlspecialchars($row['guide'] ?? '0') . '</td>';
            $html .= '<td>' . htmlspecialchars($row['foc'] ?? '0') . '</td>';
            $html .= '<td>' . htmlspecialchars($row['tl'] ?? '0') . '</td>';
            $html .= '<td>' . number_format($row['amount'] ?? 0, 2) . '</td>';
            $html .= '<td>' . htmlspecialchars($row['payment_status'] ?? '') . '</td>';
            $html .= '<td>' . htmlspecialchars($row['pay_type'] ?? '') . '</td>';
            $html .= '<td>' . htmlspecialchars($row['book_status'] ?? '') . '</td>';
            $html .= '<td>' . htmlspecialchars($row['agent'] ?? '') . '</td>';
            $html .= '<td>' . htmlspecialchars($row['voucher'] ?? '') . '</td>';
            $html .= '<td>' . htmlspecialchars($row['remark'] ?? '') . '</td>';
            $html .= '<td>' . htmlspecialchars($row['create_date'] ?? '') . '</td>';
            $html .= '</tr>';
        }
        
        $html .= '</tbody>';
        $html .= '</table>';
        
        // Add summary
        $totalBookings = count($data);
        $totalAmount = array_sum(array_column($data, 'amount'));
        $totalAdult = array_sum(array_column($data, 'adult'));
        $totalChild = array_sum(array_column($data, 'child'));
        $totalInfant = array_sum(array_column($data, 'infant'));
        
        $html .= '<br><br>';
        $html .= '<h3>Summary</h3>';
        $html .= '<table style="width: 50%;">';
        $html .= '<tr><td><strong>Total Bookings:</strong></td><td>' . $totalBookings . '</td></tr>';
        $html .= '<tr><td><strong>Total Amount:</strong></td><td>฿' . number_format($totalAmount, 2) . '</td></tr>';
        $html .= '<tr><td><strong>Total Adults:</strong></td><td>' . $totalAdult . '</td></tr>';
        $html .= '<tr><td><strong>Total Children:</strong></td><td>' . $totalChild . '</td></tr>';
        $html .= '<tr><td><strong>Total Infants:</strong></td><td>' . $totalInfant . '</td></tr>';
        $html .= '<tr><td><strong>Total Guests:</strong></td><td>' . ($totalAdult + $totalChild + $totalInfant) . '</td></tr>';
        $html .= '</table>';
    }
    
    $html .= '</body>';
    $html .= '</html>';
    
    return $html;
}
?>
