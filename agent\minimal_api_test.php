<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Start session
session_start();

// Set headers
header('Content-Type: application/json');

echo "Starting minimal API test...\n";

// Check session
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

echo "Session OK...\n";

try {
    // Include database connection
    include '../dbconnect/_dbconnect.php';
    echo "Database connection included...\n";
    
    // Connect to database
    $conn = db_connect();
    echo "Database connected...\n";
    
    // Test simple query first
    $sql = "SELECT COUNT(*) as count FROM kp_agent";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Agent count: " . $result['count'] . "\n";
    
    // Test search parameter
    $search = isset($_GET['search']) ? $_GET['search'] : '';
    echo "Search parameter: '{$search}'\n";
    
    if (!empty($search)) {
        echo "Preparing search query...\n";
        
        // Try the problematic query
        $sql = "SELECT * FROM kp_agent WHERE name LIKE :search OR contact_person LIKE :search OR phone LIKE :search OR email LIKE :search ORDER BY name ASC";
        echo "SQL: {$sql}\n";
        
        $stmt = $conn->prepare($sql);
        echo "Query prepared...\n";
        
        $searchParam = "%{$search}%";
        echo "Search param: '{$searchParam}'\n";
        
        // Try bindValue
        $stmt->bindValue(':search', $searchParam, PDO::PARAM_STR);
        echo "Parameter bound...\n";
        
        $stmt->execute();
        echo "Query executed...\n";
        
        $agents = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "Results fetched: " . count($agents) . " agents\n";
        
        echo json_encode(['success' => true, 'data' => $agents, 'debug' => 'Search completed successfully']);
    } else {
        echo "Getting all agents...\n";
        
        $sql = "SELECT * FROM kp_agent ORDER BY name ASC";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $agents = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(['success' => true, 'data' => $agents, 'debug' => 'All agents retrieved']);
    }
    
    // Close connection
    db_close($conn);
    
} catch (PDOException $e) {
    echo "PDO Exception: " . $e->getMessage() . "\n";
    echo "Error Code: " . $e->getCode() . "\n";
    echo json_encode(['success' => false, 'message' => 'PDO Error: ' . $e->getMessage(), 'code' => $e->getCode()]);
} catch (Exception $e) {
    echo "General Exception: " . $e->getMessage() . "\n";
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?>
