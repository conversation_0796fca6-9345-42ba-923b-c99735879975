<?php
/**
 * Test Disabled Tables Display
 * This file tests the disabled tables functionality and JSON response
 */

// Start session
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('location:../login');
    exit;
}

include '../dbconnect/_dbconnect.php';

// Test parameters
$testDate = '2025-07-26';
$testZone = '1';

echo "<h2>Testing Disabled Tables Display</h2>";
echo "<p><strong>Test Date:</strong> {$testDate}</p>";
echo "<p><strong>Test Zone:</strong> {$testZone}</p>";

try {
    // Connect to database
    $conn = db_connect();
    
    // Check if kp_tables table exists
    $checkTable = $conn->query("SHOW TABLES LIKE 'kp_tables'");
    if ($checkTable->rowCount() == 0) {
        echo "<div style='color: red;'>❌ kp_tables table does not exist!</div>";
        echo "<p>Creating test table structure...</p>";
        
        // Create test table
        $createTable = "CREATE TABLE IF NOT EXISTS `kp_tables` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `table_id` varchar(50) NOT NULL,
            `floor` varchar(10) NOT NULL,
            `status` enum('active','disabled') DEFAULT 'active',
            `disabled_until` date DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `table_floor_unique` (`table_id`, `floor`)
        )";
        
        $conn->exec($createTable);
        echo "<div style='color: green;'>✅ kp_tables table created successfully!</div>";
        
        // Insert test disabled tables
        $testTables = [
            ['C12', '1', 'disabled', '2025-08-01'],
            ['C14', '1', 'disabled', null], // Permanently disabled
            ['C15', '1', 'disabled', '2025-07-25'], // Should not appear (expired)
            ['H1', '2', 'disabled', '2025-08-01'],
            ['A1', '3', 'disabled', '2025-08-01']
        ];
        
        $insertStmt = $conn->prepare("INSERT IGNORE INTO kp_tables (table_id, floor, status, disabled_until) VALUES (?, ?, ?, ?)");
        
        foreach ($testTables as $table) {
            $insertStmt->execute($table);
        }
        
        echo "<div style='color: green;'>✅ Test disabled tables inserted!</div>";
    } else {
        echo "<div style='color: green;'>✅ kp_tables table exists</div>";
    }
    
    // Test the API call
    echo "<h3>Testing API Response</h3>";
    $apiUrl = "get_disabled_tables.php?useZone={$testZone}&useDate={$testDate}";
    echo "<p><strong>API URL:</strong> <a href='{$apiUrl}' target='_blank'>{$apiUrl}</a></p>";
    
    // Make internal API call
    $selectedDate = $testDate;
    $selectedFloor = $testZone;
    
    $sql = "SELECT `table_id`, `status`, `disabled_until`, `floor` FROM `kp_tables`
            WHERE `status` = 'disabled'
            AND `floor` = :selectedFloor
            AND (
                `disabled_until` IS NULL
                OR `disabled_until` >= :selectedDate
            )";

    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':selectedDate', $selectedDate);
    $stmt->bindParam(':selectedFloor', $selectedFloor);
    $stmt->execute();
    
    $disabledTables = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $disabledTables[] = [
            'id' => $row['table_id'],
            'status' => $row['status'],
            'floor' => $row['floor'],
            'disabled_until' => $row['disabled_until'],
            'selected_date' => $selectedDate
        ];
    }
    
    $response = [
        'status' => 'success',
        'selected_date' => $selectedDate,
        'selected_floor' => $selectedFloor,
        'count' => count($disabledTables),
        'tables' => $disabledTables
    ];
    
    echo "<h4>API Response:</h4>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
    echo json_encode($response, JSON_PRETTY_PRINT);
    echo "</pre>";
    
    if (count($disabledTables) > 0) {
        echo "<div style='color: green;'>✅ Found " . count($disabledTables) . " disabled tables for zone {$testZone}</div>";
        
        echo "<h4>Disabled Tables Details:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Table ID</th><th>Floor</th><th>Status</th><th>Disabled Until</th></tr>";
        
        foreach ($disabledTables as $table) {
            $disabledUntil = $table['disabled_until'] ? $table['disabled_until'] : 'Permanently';
            echo "<tr>";
            echo "<td>{$table['id']}</td>";
            echo "<td>{$table['floor']}</td>";
            echo "<td>{$table['status']}</td>";
            echo "<td>{$disabledUntil}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div style='color: orange;'>⚠️ No disabled tables found for zone {$testZone} on {$testDate}</div>";
        echo "<p>This could mean:</p>";
        echo "<ul>";
        echo "<li>No tables are currently disabled</li>";
        echo "<li>All disabled tables have expired</li>";
        echo "<li>No disabled tables exist for this floor</li>";
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Error: " . $e->getMessage() . "</div>";
}

echo "<hr>";
echo "<h3>Testing JavaScript Integration</h3>";
echo "<p>Open the browser console and check for disabled table processing messages.</p>";
echo "<button onclick='testDisabledTables()'>Test Disabled Tables JavaScript</button>";
echo "<div id='test-results'></div>";

?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function testDisabledTables() {
    console.log('Testing disabled tables JavaScript integration...');
    
    // Test the API call
    $.ajax({
        url: 'get_disabled_tables.php',
        type: 'GET',
        data: { 
            useZone: '<?php echo $testZone; ?>',
            useDate: '<?php echo $testDate; ?>'
        },
        dataType: 'json',
        success: function(response) {
            console.log('✅ API Response received:', response);
            
            let resultHtml = '<h4>JavaScript Test Results:</h4>';
            
            if (response && response.status === 'success') {
                resultHtml += `<p>✅ Status: ${response.status}</p>`;
                resultHtml += `<p>📅 Date: ${response.selected_date}</p>`;
                resultHtml += `<p>🏢 Floor: ${response.selected_floor}</p>`;
                resultHtml += `<p>📊 Count: ${response.count}</p>`;
                
                if (response.tables && response.tables.length > 0) {
                    resultHtml += '<p>✅ Disabled tables found:</p><ul>';
                    response.tables.forEach(table => {
                        resultHtml += `<li>Table ${table.id} (Floor ${table.floor}) - Disabled until: ${table.disabled_until || 'Permanently'}</li>`;
                    });
                    resultHtml += '</ul>';
                    
                    // Test the table marking logic
                    resultHtml += '<p>🎯 Testing table marking logic...</p>';
                    
                    // Simulate the filtering logic
                    const zoneFilters = {
                        1: (table) => {
                            if (!table || !table.id) return false;
                            const id = table.id.toString().toUpperCase();
                            return id.startsWith('C');
                        }
                    };
                    
                    const filteredTables = response.tables.filter(zoneFilters[1] || (() => false));
                    resultHtml += `<p>🔍 Filtered tables for zone 1: ${filteredTables.length}</p>`;
                    
                    if (filteredTables.length > 0) {
                        resultHtml += '<ul>';
                        filteredTables.forEach(table => {
                            resultHtml += `<li>✅ Table ${table.id} should be marked as disabled</li>`;
                        });
                        resultHtml += '</ul>';
                    }
                } else {
                    resultHtml += '<p>ℹ️ No disabled tables found</p>';
                }
            } else {
                resultHtml += `<p>❌ Unexpected response format: ${JSON.stringify(response)}</p>`;
            }
            
            $('#test-results').html(resultHtml);
        },
        error: function(xhr, status, error) {
            console.error('❌ API Error:', error);
            console.error('Response:', xhr.responseText);
            
            $('#test-results').html(`
                <h4>JavaScript Test Results:</h4>
                <p>❌ Error: ${error}</p>
                <p>Status: ${status}</p>
                <p>Response: ${xhr.responseText}</p>
            `);
        }
    });
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
button { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; }
button:hover { background: #005a87; }
</style>
