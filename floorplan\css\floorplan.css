/* Floorplan CSS */

/* Table styles */
.svg-box {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.svg-box:hover rect {
    stroke-width: 3px;
    stroke: #ffffff;
}

/* Booked table styles */
.booked-disabled {
    cursor: not-allowed !important;
    opacity: 0.95;
    pointer-events: none !important;
}

.booked-disabled rect {
    fill: #999999 !important;
}

/* Additional selector to ensure booked tables can't be clicked */
[data-booked="true"] {
    pointer-events: none !important;
}

/* Enhanced reservation label styles */
.booked-label,
.reservation-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(255, 0, 0, 0.7);
    color: white;
    font-size: 10px;
    font-weight: bold;
    padding: 2px 4px;
    border-radius: 3px;
    white-space: nowrap;
    z-index: 10;
    pointer-events: none;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
}

/* Status-specific label colors */
.pending-reservation .reservation-label {
    background-color: rgba(255, 152, 0, 0.8);
    color: white;
}

.confirmed-reservation .reservation-label {
    background-color: rgba(76, 175, 80, 0.8);
    color: white;
}

.cancelled-reservation .reservation-label {
    background-color: rgba(244, 67, 54, 0.8);
    color: white;
}

.table-disabled .reservation-label {
    background-color: rgba(153, 153, 153, 0.8);
    color: white;
}

/* Floor buyout indicator */
.floor-buyout-indicator {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(220, 53, 69, 0.5);
    z-index: 5;
    pointer-events: auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

.floor-buyout-text {
    background-color: rgba(220, 53, 69, 0.9);
    color: white;
    font-size: 24px;
    font-weight: bold;
    padding: 10px 20px;
    border-radius: 5px;
    text-transform: uppercase;
    letter-spacing: 2px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Disabled table styles */
.table-disabled {
    cursor: not-allowed !important;
    opacity: 0.6;
}

.table-disabled rect {
    fill: #999999 !important;
}

/* Selected table styles */
.svg-box[data-value="1"] rect {
    fill: #00FF00 !important;
    /* Green */
}

/* Enhanced reservation status styles */
.pending-reservation {
    cursor: pointer !important;
    opacity: 0.9;
}

.pending-reservation rect {
    fill: #ff9800 !important;
    stroke: #f57c00;
    stroke-width: 2px;
    stroke-dasharray: 3, 3;
    animation: pulse-pending 2s infinite;
}

.confirmed-reservation {
    cursor: not-allowed !important;
    opacity: 0.95;
    pointer-events: none !important;
}

.confirmed-reservation rect {
    fill: #4caf50 !important;
    stroke: #388e3c;
    stroke-width: 2px;
}

.cancelled-reservation {
    cursor: not-allowed !important;
    opacity: 0.7;
    pointer-events: none !important;
}

.cancelled-reservation rect {
    fill: #f44336 !important;
    stroke: #d32f2f;
    stroke-width: 2px;
    stroke-dasharray: 5, 5;
}

/* Animations for different reservation states */
@keyframes pulse-pending {
    0% {
        opacity: 0.9;
    }

    50% {
        opacity: 0.6;
    }

    100% {
        opacity: 0.9;
    }
}

@keyframes flash-new-booking {

    0%,
    100% {
        opacity: 1;
    }

    25%,
    75% {
        opacity: 0.3;
    }

    50% {
        opacity: 0.6;
    }
}

/* New booking animation */
.new-booking {
    animation: flash-new-booking 1.5s ease-in-out 3;
}

/* Status change animation */
.status-change-animation {
    animation: status-change 1.5s ease-in-out;
}

@keyframes status-change {
    0% {
        transform: scale(1);
    }

    25% {
        transform: scale(1.1);
    }

    50% {
        transform: scale(1.05);
    }

    75% {
        transform: scale(1.1);
    }

    100% {
        transform: scale(1);
    }
}

/* Reservation statistics styles */
.reservation-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 5px;
    margin-top: 10px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 80px;
}

.stat-label {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 2px;
}

.stat-value {
    font-size: 1rem;
    font-weight: bold;
    color: #333;
}

/* Hover effects for different reservation states */
.pending-reservation:hover rect {
    fill: #ffb74d !important;
    stroke-width: 3px;
}

.svg-box:not(.booked-disabled):not(.table-disabled):not(.confirmed-reservation):not(.cancelled-reservation):hover rect {
    opacity: 0.8;
    stroke-width: 3px;
    transition: all 0.2s ease;
}

/* Floor tab styles */
.nav-tabs .nav-link {
    position: relative;
}

.nav-tabs .nav-link .badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
}

.nav-tabs .nav-link .progress {
    height: 4px;
    width: 50px;
    background-color: rgba(255, 255, 255, 0.2);
}

/* Color coding for different rows */
/* Floor 1 */
.floor1-row-a rect {
    fill: #539bff;
    /* Blue */
}

/* Floor 2 */
.floor2-row-b rect {
    fill: #ffaa00;
    /* Yellow */
}

.floor2-row-c rect {
    fill: #ff7700;
    /* Orange */
}

.floor2-row-d rect {
    fill: #ff5584;
    /* Pink */
}

/* Floor 3 */
.floor3-row-e rect {
    fill: #12deb9;
    /* Teal */
}

.floor3-row-f rect {
    fill: #00b33c;
    /* Green */
}

.floor3-row-g rect {
    fill: #73e600;
    /* Light Green */
}

.floor3-row-p rect {
    fill: #ff5584;
    /* Pink */
}

/* Tooltip styles */
.tooltip {
    position: absolute;
    z-index: 1070;
    display: block;
    margin: 0;
    font-family: var(--bs-font-sans-serif);
    font-style: normal;
    font-weight: 400;
    line-height: 1.5;
    text-align: left;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    letter-spacing: normal;
    word-break: normal;
    word-spacing: normal;
    white-space: normal;
    line-break: auto;
    font-size: 0.875rem;
    word-wrap: break-word;
    opacity: 0;
}

.tooltip.show {
    opacity: 0.9;
}

.tooltip .tooltip-inner {
    max-width: 200px;
    padding: 0.25rem 0.5rem;
    color: #fff;
    text-align: center;
    background-color: #000;
    border-radius: 0.25rem;
}

/* Datepicker styles */
.datepicker {
    z-index: 1060 !important;
    /* Ensure datepicker appears above other elements */
}

.datepicker-dropdown {
    padding: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.datepicker table tr td.active,
.datepicker table tr td.active:hover,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active.disabled:hover {
    background-color: #635bff !important;
    background-image: none !important;
}

.datepicker table tr td.today,
.datepicker table tr td.today:hover,
.datepicker table tr td.today.disabled,
.datepicker table tr td.today.disabled:hover {
    background-color: #ffdb99 !important;
    background-image: none !important;
}

.input-group.date .input-group-text {
    cursor: pointer;
    background-color: #f8f9fa;
    border-color: #ced4da;
}

.input-group.date .form-control {
    cursor: pointer;
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.85);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(2px);
    /* Add blur effect for modern browsers */
}

.spinner-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 25px;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.05);
    min-width: 200px;
    text-align: center;
}

.swal2-confirm.btn-primary {
    background: #6a5cff;
    color: #fff;
    padding: 8px 24px;
    border-radius: 6px;
    font-weight: bold;
}

.swal2-cancel.btn-danger {
    background: #ff6a8b;
    color: #fff;
    padding: 8px 24px;
    border-radius: 6px;
    font-weight: bold;
}

/* Enhanced Modal Styling */
#bookingModal .modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

#bookingModal .modal-header {
    background: white;
    color: #333;
    border-radius: 12px 12px 0 0;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

#bookingModal .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

#bookingModal .btn-close {
    opacity: 0.6;
}

#bookingModal .btn-close:hover {
    opacity: 1;
}

#bookingModal .modal-body {
    padding: 1.5rem;
}

#bookingModal .form-label {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    color: #495057;
    font-weight: 600;
}

#bookingModal .form-control {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 0.6rem 0.75rem;
    font-size: 0.9rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#bookingModal .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

#bookingModal .form-control[readonly] {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
}

#bookingModal .form-control::placeholder {
    color: #adb5bd;
    font-style: italic;
}

#bookingModal .form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

#bookingModal .form-check-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
}

#bookingModal .form-check-label {
    font-size: 0.9rem;
    color: #495057;
    cursor: pointer;
}

#bookingModal .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
    border-radius: 4px;
    font-weight: 500;
}

#bookingModal .bg-light {
    background-color: #f8f9fa !important;
    border: 1px solid #e9ecef;
}

#bookingModal .modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
    background-color: #f8f9fa;
    border-radius: 0 0 12px 12px;
}

#bookingModal .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 6px;
    padding: 0.6rem 1.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

#bookingModal .btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

#bookingModal .btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
    border-radius: 6px;
    padding: 0.6rem 1.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

#bookingModal .btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
    transform: translateY(-1px);
}

/* Number input styling */
#bookingModal input[type="number"] {
    text-align: center;
}

/* Row spacing */
#bookingModal .row.g-3>* {
    margin-bottom: 0.75rem;
}

#bookingModal .form-label {
    margin-bottom: 0.25rem !important;
}

#bookingModal .form-control {
    padding: 0.3rem !important;
}

#bookingModal .modal-header {
    padding: 0.6rem 2rem !important;
}

.form-select {
    padding: 0.3rem !important;
}

.theme-tab.nav-tabs .nav-item .nav-link {
    padding: 0.5rem 1rem !important;
}

/* Dynamic Table Disabled Styling */
.table-disabled {
    opacity: 0.2;
    cursor: not-allowed !important;
}

.table-disabled rect {
    fill: #6c757d !important;
    /* Gray color for disabled tables */
    stroke: #495057 !important;
}

.table-disabled text {
    fill: #ffffff !important;
    opacity: 0.7;
}

/* Hover effects for non-disabled tables */
.svg-box:not(.table-disabled):hover rect {
    stroke-width: 3;
    filter: brightness(1.1);
}

/* Ensure disabled tables don't have hover effects */
.table-disabled:hover rect {
    stroke-width: 2 !important;
    filter: none !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .nav-tabs .nav-link .progress {
        width: 30px;
    }

    .nav-tabs .nav-link .badge {
        font-size: 0.6rem;
        padding: 0.1rem 0.3rem;
    }

    .datepicker-dropdown {
        width: 280px;
    }
}