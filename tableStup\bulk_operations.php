<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Set content type to JSON
header('Content-Type: application/json');

include '../dbconnect/_dbconnect.php';

try {
    // Validate input
    if (!isset($_POST['operation']) || !isset($_POST['floor'])) {
        throw new Exception('Missing required parameters');
    }



    $operation = $_POST['operation'];
    $floor = $_POST['floor'];
    $rows = isset($_POST['rows']) ? json_decode($_POST['rows'], true) : [];
    $disabledUntil = isset($_POST['disabled_until']) ? $_POST['disabled_until'] : null;



    // Validate operation
    if (!in_array($operation, ['enable', 'disable'])) {
        throw new Exception('Invalid operation');
    }

    // Validate floor
    if (!in_array($floor, ['1', '2', '3'])) {
        throw new Exception('Invalid floor');
    }

    // Connect to database
    $conn = db_connect();

    // Build WHERE clause based on floor and rows
    $whereConditions = [];
    $params = [];

    // Floor-based filtering
    if ($floor === '1') {
        $floorRows = ['VIP', 'C1', 'C2', 'C3', 'C4', 'C5', 'C6', 'C7', 'C8', 'C9', 'C10', 'C11', 'C12', 'C14', 'C15', 'C16', 'C17', 'C18', 'C19', 'C20', 'C21', 'C22', 'C23'];
    } elseif ($floor === '2') {
        $floorRows = ['H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'H7', 'H8', 'H9', 'H10', 'H11', 'H12', 'H14', 'H15', 'H16', 'H17', 'B1', 'B2', 'B3', 'B4', 'B5', 'B6', 'B7', 'B8', 'B9', 'B10', 'B11', 'B12', 'B14', 'B15', 'B16', 'B17', 'B18', 'B19', 'B20', 'B21', 'B22', 'B23', 'B24', 'B25', 'B26'];
    } elseif ($floor === '3') {
        $floorRows = ['A1', 'A2', 'A3', 'A4', 'A5', 'A6', 'A7', 'A8', 'A9', 'A10', 'A11', 'A12', 'A14', 'A15', 'A16', 'A17', 'A18', 'A19', 'A20', 'A21', 'A22', 'A23', 'A24', 'A25', 'A26', 'A27', 'A28', 'A29', 'A30', 'A31', 'A32', 'A33', 'A34', 'A35', 'A36'];
    }



    // If specific rows are selected, filter by them
    if (!empty($rows) && !in_array('all', $rows)) {
        $validRows = array_intersect($rows, $floorRows);
        if (empty($validRows)) {
            throw new Exception('No valid rows selected');
        }
        $floorRows = $validRows;
    }

    // echo json_encode(['success' => true, 'message' => 'Floor rows: ' . implode(', ', $floorRows)]);
    // exit;

    // Build row conditions
    $rowConditions = [];
    foreach ($floorRows as $index => $row) {
        $paramKey = ":row_$index";

        if ($row === 'VIP') {
            // Handle VIP tables specifically - use literal values, no parameters needed
            $rowConditions[] = "(table_id = 'VIP1' OR table_id = 'VIP2')";
        } elseif ($row === 'B25' || $row === 'B26') {
            // Handle B25 and B26 as standalone tables only
            $rowConditions[] = "table_id = $paramKey";
            $params[$paramKey] = $row;
        } else {
            // Handle other tables (both standalone and with suffixes)
            $rowConditions[] = "(table_id = $paramKey OR table_id LIKE CONCAT($paramKey, '-%'))";
            $params[$paramKey] = $row;
        }
    }

    // echo json_encode(['success' => true, 'message' => 'Row conditions: ' . implode(', ', $rowConditions).'<br>Params: ' . json_encode($params)]);
    // exit;
    // {"success":true,"message":"Row conditions: (table_id = :row_0 OR table_id LIKE CONCAT(:row_0, '-%'))<br>Params: {\":row_0\":\"C1\"}"}

    // Check if we have any row conditions
    if (empty($rowConditions)) {
        throw new Exception('No valid rows to process');
    }

    // echo json_encode(['success' => true, 'message' => 'Row conditions: ' . implode(', ', $rowConditions).'<br>Params: ' . json_encode($params)]);
    // exit;

    // Add floor condition for better performance
    $whereClause = 'floor = :floor AND (' . implode(' OR ', $rowConditions) . ')';
    $params[':floor'] = (int)$floor;

    // echo json_encode(['success' => true, 'message' => 'Where clause: ' . $whereClause . '<br>Params: ' . json_encode($params)]);
    // exit;

    // Validate date if disabling
    if ($operation === 'disable' && $disabledUntil !== null && $disabledUntil !== '') {
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $disabledUntil)) {
            throw new Exception('Invalid date format. Use YYYY-MM-DD');
        }

        // Check if date is in the future
        $disabledDate = new DateTime($disabledUntil);
        $disabledDate->setTime(0, 0, 0);

        $today = new DateTime();
        $today->setTime(0, 0, 0);

        if ($disabledDate <= $today) {
            throw new Exception('Disabled until date must be in the future');
        }

    }


    // Build and execute update query
    if ($operation === 'enable') {
        $sql = "UPDATE kp_tables SET status = 'active', disabled_until = NULL WHERE $whereClause";
    } else {
        if ($disabledUntil) {
            $sql = "UPDATE kp_tables SET status = 'disabled', disabled_until = :disabled_until WHERE $whereClause";
            $params[':disabled_until'] = $disabledUntil;
        } else {
            $sql = "UPDATE kp_tables SET status = 'disabled', disabled_until = NULL WHERE $whereClause";
        }
    }

    // echo json_encode(['success' => true, 'message' => 'SQL: ' . $sql . '<br>Params: ' . json_encode($params)]);
    // exit;

    $stmt = $conn->prepare($sql);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();

    $affectedRows = $stmt->rowCount();

    echo json_encode(['success' => true, 'message' => 'Affected rows: ' . $affectedRows . '<br>SQL: ' . $sql . '<br>Params: ' . json_encode($params)]);
    exit;

    // Return success with count
    echo json_encode([
        'success' => true,
        'message' => "Successfully {$operation}d $affectedRows tables",
        'affected_rows' => $affectedRows
    ]);

} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
