<div id="main-wrapper">
    <!-- Sidebar Start -->
    <aside class="side-mini-panel with-vertical">
        <!-- ---------------------------------- -->
        <!-- Start Vertical Layout Sidebar -->
        <!-- ---------------------------------- -->
        <div class="iconbar">
            <div>

                <?php
                    include_once("../_menuMain.php");
                    include_once("_menuPages.php");
                ?>

            </div>
        </div>
    </aside>
    <!--  Sidebar End -->


    <div class="page-wrapper">
        <?php
            include_once("_menuTop.php");
        ?>

        <div class="body-wrapper">
            <div class="container-fluid">
                <div class="row">                    

                    <div class="col-lg-12">

                        <!-- Report Header -->
                        <!-- <div class="card card-body mb-3 shadow-sm">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h4 class="mb-1">
                                        <iconify-icon icon="solar:chart-2-bold-duotone" class="fs-4 me-2"></iconify-icon>
                                        Date Range Report
                                    </h4>
                                    <p class="text-muted mb-0">Generate comprehensive reports for any date range with detailed analytics</p>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-info" onclick="window.open('test_api.php', '_blank')">
                                        <iconify-icon icon="solar:bug-bold-duotone" class="fs-5 me-2"></iconify-icon>Test API
                                    </button>
                                </div>
                            </div>
                        </div> -->

                        <!-- Date Range Selection -->
                        <div class="card card-body mb-3" id="date-range-card">
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="from-date" class="form-label">From Date</label>
                                    <input type="date" class="form-control" id="from-date" value="<?php echo date('Y-m-d'); ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="to-date" class="form-label">To Date</label>
                                    <input type="date" class="form-control" id="to-date" value="<?php echo date('Y-m-d'); ?>">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Quick Select</label>
                                    <div class="btn-group d-block" role="group">
                                        <button type="button" class="btn btn-outline-primary btn-sm me-1" onclick="setDateRange('today')">Today</button>
                                        <button type="button" class="btn btn-outline-primary btn-sm me-1" onclick="setDateRange('yesterday')">Yesterday</button>
                                        <button type="button" class="btn btn-outline-primary btn-sm me-1" onclick="setDateRange('week')">This Week</button>
                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="setDateRange('lastweek')">Last Week</button>
                                    </div>
                                    <div class="btn-group d-block mt-1" role="group">
                                        <button type="button" class="btn btn-outline-secondary btn-sm me-1" onclick="setDateRange('month')">This Month</button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm me-1" onclick="setDateRange('lastmonth')">Last Month</button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm me-1" onclick="setDateRange('quarter')">This Quarter</button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setDateRange('year')">This Year</button>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12 export-buttons">
                                    <button type="button" class="btn btn-primary btn-lg" id="generate-report">
                                        <iconify-icon icon="solar:chart-2-bold-duotone" class="fs-5 me-2"></iconify-icon>Generate Report
                                    </button>
                                    <button type="button" class="btn btn-success btn-lg" id="export-report-excel">
                                        <iconify-icon icon="solar:file-text-bold-duotone" class="fs-5 me-2"></iconify-icon>Export Excel
                                    </button>
                                    <!-- <button type="button" class="btn btn-danger btn-lg" id="export-report-pdf">
                                        <iconify-icon icon="solar:file-bold-duotone" class="fs-5 me-2"></iconify-icon>Export PDF
                                    </button> -->
                                </div>
                            </div>
                        </div>

                        <!-- Report Summary -->
                         <div class="card card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="card-title mb-0">
                                    <iconify-icon icon="solar:chart-bar-bold-duotone" class="fs-5 me-2"></iconify-icon><span id="report-title">Daily Summary</span>
                                </h5>
                                <small class="text-muted" id="summary-date">Select parameters and generate report</small>
                            </div>
                            <!-- Summary Cards -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white summary-card">
                                        <div class="card-body text-center">
                                            <iconify-icon icon="solar:bookmark-bold-duotone" class="fs-1 mb-2"></iconify-icon>
                                            <h3 id="total-bookings" class="mb-1">0</h3>
                                            <p class="mb-0">Total Bookings</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success text-white summary-card">
                                        <div class="card-body text-center">
                                            <iconify-icon icon="solar:dollar-bold-duotone" class="fs-1 mb-2"></iconify-icon>
                                            <h3 id="total-amount" class="mb-1">฿0.00</h3>
                                            <p class="mb-0">Total Revenue</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info text-white summary-card">
                                        <div class="card-body text-center">
                                            <iconify-icon icon="solar:users-group-rounded-bold-duotone" class="fs-1 mb-2"></iconify-icon>
                                            <h3 id="total-guests" class="mb-1">0</h3>
                                            <p class="mb-0">Total Guests</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-warning text-white summary-card">
                                        <div class="card-body text-center">
                                            <iconify-icon icon="solar:chart-square-bold-duotone" class="fs-1 mb-2"></iconify-icon>
                                            <h3 id="avg-booking-value" class="mb-1">฿0.00</h3>
                                            <p class="mb-0">Avg Booking Value</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Detailed Summary Table -->
                            <div class="table-responsive">
                                <table class="table table-striped align-middle text-nowrap">
                                    <thead class="table-dark">
                                        <tr>
                                            <th class="text-center">Metric</th>
                                            <th class="text-center">Count</th>
                                            <th class="text-center">Percentage</th>
                                        </tr>
                                    </thead>
                                    <tbody id="detailed-summary">
                                        <tr>
                                            <td><strong>Adults</strong></td>
                                            <td id="total-adult" class="text-center fw-bold">0</td>
                                            <td id="adult-percentage" class="text-center">0%</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Children</strong></td>
                                            <td id="total-child" class="text-center fw-bold">0</td>
                                            <td id="child-percentage" class="text-center">0%</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Infants</strong></td>
                                            <td id="total-infant" class="text-center fw-bold">0</td>
                                            <td id="infant-percentage" class="text-center">0%</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Guides</strong></td>
                                            <td id="total-guide" class="text-center fw-bold">0</td>
                                            <td id="guide-percentage" class="text-center">0%</td>
                                        </tr>
                                        <tr>
                                            <td><strong>FOC</strong></td>
                                            <td id="total-foc" class="text-center fw-bold">0</td>
                                            <td id="foc-percentage" class="text-center">0%</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Team Leaders</strong></td>
                                            <td id="total-tl" class="text-center fw-bold">0</td>
                                            <td id="tl-percentage" class="text-center">0%</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Charts Section -->
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="card-title mb-0">Guest Distribution</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="guestChart" width="400" height="200"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="card-title mb-0">Payment Status</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="paymentChart" width="400" height="200"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Floor Distribution Chart -->
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="card-title mb-0">Floor Distribution</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="floorChart" width="800" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Booking Status Summary -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <iconify-icon icon="solar:clipboard-list-bold-duotone" class="fs-5 me-2"></iconify-icon>Booking Status Summary
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped" id="status-summary-table">
                                        <thead>
                                            <tr>
                                                <th>Status</th>
                                                <th>Count</th>
                                                <th>Total People</th>
                                                <th>Total Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Data will be populated by JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Agent Performance Summary -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <iconify-icon icon="solar:user-id-bold-duotone" class="fs-5 me-2"></iconify-icon>Agent Performance Summary
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped" id="agent-summary-table">
                                        <thead>
                                            <tr>
                                                <th>Agent Name</th>
                                                <th>Bookings</th>
                                                <th>Total People</th>
                                                <th>Total Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Data will be populated by JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- User Activity Summary -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <iconify-icon icon="solar:users-group-two-rounded-bold-duotone" class="fs-5 me-2"></iconify-icon>User Activity Summary
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped" id="user-summary-table">
                                        <thead>
                                            <tr>
                                                <th>User</th>
                                                <th>Bookings</th>
                                                <th>Total People</th>
                                                <th>Total Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Data will be populated by JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                    </div>
                    

                </div>

            </div>
        </div>


        <div class="offcanvas customizer offcanvas-end" tabindex="-1" id="offcanvasExample"
            aria-labelledby="offcanvasExampleLabel">
            <div class="d-flex align-items-center justify-content-between p-3 border-bottom">
                <h4 class="offcanvas-title fw-semibold" id="offcanvasExampleLabel">
                    Settings
                </h4>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body" data-simplebar style="height: calc(100vh - 80px)">
                <h6 class="fw-semibold fs-4 mb-2">Theme</h6>

                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <input type="radio" class="btn-check light-layout" name="theme-layout" id="light-layout"
                        autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="light-layout">
                        <i class="icon ti ti-brightness-up fs-7 me-2"></i>Light
                    </label>

                    <input type="radio" class="btn-check dark-layout" name="theme-layout" id="dark-layout"
                        autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="dark-layout">
                        <i class="icon ti ti-moon fs-7 me-2"></i>Dark
                    </label>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Theme Direction</h6>
                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <input type="radio" class="btn-check" name="direction-l" id="ltr-layout" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="ltr-layout">
                        <i class="icon ti ti-text-direction-ltr fs-7 me-2"></i>LTR
                    </label>

                    <input type="radio" class="btn-check" name="direction-l" id="rtl-layout" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="rtl-layout">
                        <i class="icon ti ti-text-direction-rtl fs-7 me-2"></i>RTL
                    </label>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Theme Colors</h6>

                <div class="d-flex flex-row flex-wrap gap-3 customizer-box color-pallete" role="group">
                    <input type="radio" class="btn-check" name="color-theme-layout" id="Blue_Theme"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Blue_Theme')" for="Blue_Theme" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="BLUE_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-1">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="Aqua_Theme"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Aqua_Theme')" for="Aqua_Theme" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="AQUA_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-2">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="Purple_Theme"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Purple_Theme')" for="Purple_Theme" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="PURPLE_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-3">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="green-theme-layout"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Green_Theme')" for="green-theme-layout" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="GREEN_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-4">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="cyan-theme-layout"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Cyan_Theme')" for="cyan-theme-layout" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="CYAN_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-5">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="orange-theme-layout"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Orange_Theme')" for="orange-theme-layout" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="ORANGE_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-6">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Layout Type</h6>
                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <div>
                        <input type="radio" class="btn-check" name="page-layout" id="vertical-layout"
                            autocomplete="off" />
                        <label class="btn p-9 btn-outline-primary rounded-2" for="vertical-layout">
                            <i class="icon ti ti-layout-sidebar-right fs-7 me-2"></i>Vertical
                        </label>
                    </div>
                    <div>
                        <input type="radio" class="btn-check" name="page-layout" id="horizontal-layout"
                            autocomplete="off" />
                        <label class="btn p-9 btn-outline-primary rounded-2" for="horizontal-layout">
                            <i class="icon ti ti-layout-navbar fs-7 me-2"></i>Horizontal
                        </label>
                    </div>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Container Option</h6>

                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <input type="radio" class="btn-check" name="layout" id="boxed-layout" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="boxed-layout">
                        <i class="icon ti ti-layout-distribute-vertical fs-7 me-2"></i>Boxed
                    </label>

                    <input type="radio" class="btn-check" name="layout" id="full-layout" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="full-layout">
                        <i class="icon ti ti-layout-distribute-horizontal fs-7 me-2"></i>Full
                    </label>
                </div>

                <h6 class="fw-semibold fs-4 mb-2 mt-5">Sidebar Type</h6>
                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <a href="javascript:void(0)" class="fullsidebar">
                        <input type="radio" class="btn-check" name="sidebar-type" id="full-sidebar"
                            autocomplete="off" />
                        <label class="btn p-9 btn-outline-primary rounded-2" for="full-sidebar">
                            <i class="icon ti ti-layout-sidebar-right fs-7 me-2"></i>Full
                        </label>
                    </a>
                    <div>
                        <input type="radio" class="btn-check" name="sidebar-type" id="mini-sidebar"
                            autocomplete="off" />
                        <label class="btn p-9 btn-outline-primary rounded-2" for="mini-sidebar">
                            <i class="icon ti ti-layout-sidebar fs-7 me-2"></i>Collapse
                        </label>
                    </div>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Card With</h6>

                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <input type="radio" class="btn-check" name="card-layout" id="card-with-border" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="card-with-border">
                        <i class="icon ti ti-border-outer fs-7 me-2"></i>Border
                    </label>

                    <input type="radio" class="btn-check" name="card-layout" id="card-without-border"
                        autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="card-without-border">
                        <i class="icon ti ti-border-none fs-7 me-2"></i>Shadow
                    </label>
                </div>
            </div>
        </div>

        <script>
            function handleColorTheme(e) {
                document.documentElement.setAttribute("data-color-theme", e);
            }
        </script>
    </div>


</div>


<!-- Include Report JavaScript -->
<script src="js/report.js"></script>