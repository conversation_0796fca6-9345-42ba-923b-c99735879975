<?php
session_start();
require_once '../../dbconnect/_dbconnect.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}

// Check if user_key is provided
if (!isset($_GET['user_key']) || empty($_GET['user_key'])) {
    echo json_encode(['success' => false, 'message' => 'User key is required']);
    exit;
}

$userKey = $_GET['user_key'];

try {
    // Get database connection
    $pdo = db_connect();

    // Get user information by user_key
    $stmt = $pdo->prepare("SELECT user as username, name, role FROM kp_login WHERE user_key = ? AND status = 1");
    $stmt->execute([$userKey]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'user' => $user
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>
