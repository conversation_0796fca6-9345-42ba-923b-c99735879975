<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

echo "<h2>Report Generation Test</h2>";

// Test different report types
$testCases = [
    ['type' => 'daterange', 'from_date' => '2024-12-01', 'to_date' => '2024-12-31', 'description' => 'Date Range Report (Dec 2024)'],
    ['type' => 'daily', 'from_date' => '2024-12-31', 'description' => 'Daily Report (Dec 31, 2024)'],
    ['type' => 'monthly', 'month' => '2024-12', 'description' => 'Monthly Report (Dec 2024)'],
    ['type' => 'ytd', 'year' => '2024', 'description' => 'Year to Date Report (2024)'],
    ['type' => 'quarterly', 'from_date' => '2024-10-01', 'description' => 'Quarterly Report (Oct-Dec 2024)']
];

foreach ($testCases as $index => $testCase) {
    echo "<h3>" . ($index + 1) . ". {$testCase['description']}</h3>";
    
    // Build URL
    $url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/generate_report.php";
    $params = [];
    
    foreach ($testCase as $key => $value) {
        if ($key !== 'description') {
            $params[] = $key . "=" . urlencode($value);
        }
    }
    
    if (!empty($params)) {
        $url .= "?" . implode("&", $params);
    }
    
    echo "<p><strong>URL:</strong> <code>{$url}</code></p>";
    
    // Create context for the request
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'Cookie: ' . $_SERVER['HTTP_COOKIE']
            ]
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response === false) {
        echo "<p style='color: red;'>❌ Failed to get response</p>";
    } else {
        $data = json_decode($response, true);
        if ($data === null) {
            echo "<p style='color: red;'>❌ Invalid JSON response</p>";
            echo "<h4>Raw Response:</h4>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 200px; overflow-y: auto;'>" . htmlspecialchars($response) . "</pre>";
        } else {
            if ($data['success']) {
                echo "<p style='color: green;'>✅ Report generated successfully</p>";
                
                if (isset($data['data']['summary'])) {
                    $summary = $data['data']['summary'];
                    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                    echo "<h4>Summary:</h4>";
                    echo "<ul>";
                    echo "<li><strong>Total Bookings:</strong> " . ($summary['total_bookings'] ?? 0) . "</li>";
                    echo "<li><strong>Total Amount:</strong> ฿" . number_format($summary['total_amount'] ?? 0, 2) . "</li>";
                    echo "<li><strong>Total Adults:</strong> " . ($summary['total_adult'] ?? 0) . "</li>";
                    echo "<li><strong>Total Children:</strong> " . ($summary['total_child'] ?? 0) . "</li>";
                    echo "<li><strong>Total Infants:</strong> " . ($summary['total_infant'] ?? 0) . "</li>";
                    echo "</ul>";
                    echo "</div>";
                }
                
                if (isset($data['data']['user_summary']) && !empty($data['data']['user_summary'])) {
                    echo "<h4>User Summary (Top 3):</h4>";
                    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; font-size: 12px;'>";
                    echo "<tr style='background: #f0f0f0;'>";
                    echo "<th>User Key</th><th>User Name</th><th>Bookings</th><th>Amount</th><th>People</th>";
                    echo "</tr>";
                    
                    $maxUsers = min(3, count($data['data']['user_summary']));
                    for ($i = 0; $i < $maxUsers; $i++) {
                        $user = $data['data']['user_summary'][$i];
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($user['user_key'] ?? '') . "</td>";
                        echo "<td>" . htmlspecialchars($user['user_name'] ?? '') . "</td>";
                        echo "<td>" . ($user['booking_count'] ?? 0) . "</td>";
                        echo "<td>฿" . number_format($user['total_amount'] ?? 0, 2) . "</td>";
                        echo "<td>" . ($user['total_people'] ?? 0) . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                } else {
                    echo "<p><em>No user summary data available</em></p>";
                }
                
                if (isset($data['data']['agent_summary']) && !empty($data['data']['agent_summary'])) {
                    echo "<h4>Agent Summary (Top 3):</h4>";
                    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; font-size: 12px;'>";
                    echo "<tr style='background: #f0f0f0;'>";
                    echo "<th>Agent</th><th>Bookings</th><th>Amount</th><th>People</th>";
                    echo "</tr>";
                    
                    $maxAgents = min(3, count($data['data']['agent_summary']));
                    for ($i = 0; $i < $maxAgents; $i++) {
                        $agent = $data['data']['agent_summary'][$i];
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($agent['agent'] ?? '') . "</td>";
                        echo "<td>" . ($agent['booking_count'] ?? 0) . "</td>";
                        echo "<td>฿" . number_format($agent['total_amount'] ?? 0, 2) . "</td>";
                        echo "<td>" . ($agent['total_people'] ?? 0) . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                } else {
                    echo "<p><em>No agent summary data available</em></p>";
                }
                
            } else {
                echo "<p style='color: red;'>❌ Report generation failed: {$data['message']}</p>";
            }
        }
    }
    
    echo "<hr>";
}

echo "<h3>Summary</h3>";
echo "<p>✅ All report types tested</p>";
echo "<p>✅ SQL GROUP BY issues should be resolved</p>";

echo "<div style='margin-top: 20px;'>";
echo "<a href='../index.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>→ Go to Reports Page</a>";
echo "</div>";
?>
