<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('location: ../login/');
    exit;
}

// Check if user has admin privileges (you may want to add role checking here)
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Tables Administration</title>
    <link href="../assets/libs/bootstrap/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/css/styles.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title mb-0">
                            <i class="ti ti-settings me-2"></i>Dynamic Tables Administration
                        </h4>
                    </div>
                    <div class="card-body">
                        
                        <!-- System Status -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h5 class="card-title">System Status</h5>
                                        <div id="system-status">
                                            <div class="d-flex align-items-center mb-2">
                                                <span class="badge bg-secondary me-2">Dynamic Tables:</span>
                                                <span id="dynamic-status">Disabled</span>
                                            </div>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-secondary me-2">Hardcoded Tables:</span>
                                                <span id="hardcoded-status">Visible</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h5 class="card-title">Database Status</h5>
                                        <div id="db-status">
                                            <div class="d-flex align-items-center mb-2">
                                                <span class="badge bg-info me-2">Total Tables:</span>
                                                <span id="total-tables">Loading...</span>
                                            </div>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-info me-2">Active Tables:</span>
                                                <span id="active-tables">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Controls -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5>System Controls</h5>
                                <div class="btn-group" role="group">
                                    <button id="enable-dynamic" class="btn btn-success">Enable Dynamic Tables</button>
                                    <button id="disable-dynamic" class="btn btn-warning">Disable Dynamic Tables</button>
                                    <button id="toggle-hardcoded" class="btn btn-info">Toggle Hardcoded Tables</button>
                                </div>
                            </div>
                        </div>

                        <!-- Database Management -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5>Database Management</h5>
                                <div class="btn-group" role="group">
                                    <a href="populate_tables_from_svg.php" class="btn btn-primary">Populate Database</a>
                                    <button id="check-db" class="btn btn-secondary">Check Database</button>
                                    <button id="clear-db" class="btn btn-danger">Clear Table Data</button>
                                </div>
                            </div>
                        </div>

                        <!-- Test Links -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5>Testing & Navigation</h5>
                                <div class="btn-group" role="group">
                                    <a href="test_dynamic_tables.php" class="btn btn-info">Test Dynamic Tables</a>
                                    <a href="mainpage.php" class="btn btn-secondary">Main Floorplan</a>
                                    <a href="../tableStup/mainpage.php" class="btn btn-secondary">Table Setup</a>
                                </div>
                            </div>
                        </div>

                        <!-- Floor Statistics -->
                        <div class="row">
                            <div class="col-12">
                                <h5>Floor Statistics</h5>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Floor</th>
                                                <th>Total Tables</th>
                                                <th>Active Tables</th>
                                                <th>Disabled Tables</th>
                                                <th>VIP Rooms</th>
                                            </tr>
                                        </thead>
                                        <tbody id="floor-stats">
                                            <tr>
                                                <td colspan="5" class="text-center">Loading statistics...</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../assets/libs/jquery/dist/jquery.min.js"></script>
    <script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Load initial status
            loadSystemStatus();
            loadDatabaseStats();
            
            // Control buttons
            $('#enable-dynamic').on('click', function() {
                updateSystemConfig(true, true);
            });
            
            $('#disable-dynamic').on('click', function() {
                updateSystemConfig(false, false);
            });
            
            $('#toggle-hardcoded').on('click', function() {
                // This would require more complex logic to track current state
                alert('This feature requires page refresh to take effect');
            });
            
            $('#check-db').on('click', function() {
                loadDatabaseStats();
            });
            
            $('#clear-db').on('click', function() {
                if (confirm('Are you sure you want to clear all table data? This cannot be undone.')) {
                    clearTableData();
                }
            });
        });
        
        function loadSystemStatus() {
            // This would typically check a configuration file or database
            // For now, we'll show default status
            $('#dynamic-status').text('Disabled').removeClass().addClass('text-warning');
            $('#hardcoded-status').text('Visible').removeClass().addClass('text-success');
        }
        
        function loadDatabaseStats() {
            $.ajax({
                url: 'get_tables.php',
                type: 'GET',
                data: { floor: 'all', status: 'all' },
                dataType: 'json',
                success: function(data) {
                    if (data.error) {
                        $('#total-tables').text('Error');
                        $('#active-tables').text('Error');
                        return;
                    }
                    
                    const total = data.length;
                    const active = data.filter(t => t.status === 'active').length;
                    
                    $('#total-tables').text(total);
                    $('#active-tables').text(active);
                    
                    // Load floor statistics
                    loadFloorStats(data);
                },
                error: function() {
                    $('#total-tables').text('Error');
                    $('#active-tables').text('Error');
                }
            });
        }
        
        function loadFloorStats(allTables) {
            const floors = {1: [], 2: [], 3: []};
            
            allTables.forEach(table => {
                const id = table.id;
                let floor = 1; // default
                
                if (id.startsWith('2') || id === 'VIP2' || id === 'VIP3') {
                    floor = 2;
                } else if (id.startsWith('3')) {
                    floor = 3;
                }
                
                floors[floor].push(table);
            });
            
            const tbody = $('#floor-stats');
            tbody.empty();
            
            [1, 2, 3].forEach(floorNum => {
                const floorTables = floors[floorNum];
                const total = floorTables.length;
                const active = floorTables.filter(t => t.status === 'active').length;
                const disabled = total - active;
                const vip = floorTables.filter(t => t.id.includes('VIP')).length;
                
                tbody.append(`
                    <tr>
                        <td><strong>Floor ${floorNum}</strong></td>
                        <td>${total}</td>
                        <td><span class="badge bg-success">${active}</span></td>
                        <td><span class="badge bg-danger">${disabled}</span></td>
                        <td><span class="badge bg-info">${vip}</span></td>
                    </tr>
                `);
            });
        }
        
        function updateSystemConfig(enabled, replaceHardcoded) {
            // This would typically update a configuration file or database
            // For now, we'll just show a message
            const status = enabled ? 'Enabled' : 'Disabled';
            const statusClass = enabled ? 'text-success' : 'text-warning';
            
            $('#dynamic-status').text(status).removeClass().addClass(statusClass);
            
            alert(`Dynamic tables ${status.toLowerCase()}. Please refresh the main floorplan page to see changes.`);
        }
        
        function clearTableData() {
            // This would require a separate PHP endpoint to clear data
            alert('Clear database functionality would be implemented here');
        }
    </script>
</body>
</html>
