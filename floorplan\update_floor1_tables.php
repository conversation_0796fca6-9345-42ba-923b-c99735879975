<?php
/**
 * <PERSON><PERSON><PERSON> to update Floor 1 tables in kp_tables database
 * Updates with the new naming scheme: C1, C1-1, C1-2, etc.
 */

session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

include '../dbconnect/_dbconnect.php';

try {
    $conn = db_connect();
    
    echo "<h2>Updating Floor 1 Tables in kp_tables Database</h2>";
    
    // Clear existing Floor 1 data (keep VIP rooms and other floors)
    echo "<h3>Clearing existing Floor 1 table data...</h3>";
    $clearSql = "DELETE FROM kp_tables WHERE table_id NOT LIKE 'VIP%' AND table_id NOT LIKE '2%' AND table_id NOT LIKE '3%'";
    $clearStmt = $conn->prepare($clearSql);
    $clearStmt->execute();
    echo "<p style='color: orange;'>✓ Cleared existing Floor 1 table data</p>";
    
    // New Floor 1 table names as provided
    $floor1TableNames = [
        'C1', 'C1-1', 'C1-2', 'C1-3', 
        'C2', 'C2-1', 'C2-2', 'C2-3', 'C2-4', 'C2-5', 
        'C3', 'C3-1', 'C3-2', 'C3-3', 'C3-4', 'C3-5', 
        'C4', 'C4-1', 'C4-2', 'C4-3', 'C4-4', 'C4-5', 
        'C5', 'C5-1', 'C5-2', 'C5-3', 'C5-4', 
        'C6', 'C6-1', 'C6-2', 'C6-3', 'C6-4', 
        'C7', 'C7-1', 'C7-2', 'C7-3', 'C7-4', 
        'C8', 'C8-1', 'C8-2', 'C8-3', 'C8-4', 
        'C9', 'C9-1', 'C9-2', 'C9-3', 'C9-4', 'C9-5', 
        'C10', 'C10-1', 'C10-2', 'C10-3', 'C10-4', 
        'C11', 'C11-1', 'C11-2', 'C11-3', 'C11-4', 'C11-5', 
        'C12', 'C12-1', 'C12-2', 'C12-3', 
        'C14', 'C14-1', 'C14-2', 'C14-3', 'C14-4', 'C14-5', 
        'C15', 'C15-1', 'C15-2', 'C15-3', 'C15-4', 'C15-5', 
        'C16', 'C16-1', 'C16-2', 'C16-3', 'C16-4', 'C16-5', 
        'C17', 'C17-1', 'C17-2', 'C17-3', 'C17-4', 
        'C18', 'C18-1', 'C18-2', 'C18-3', 'C18-4', 
        'C19', 'C19-1', 'C19-2', 'C19-3', 'C19-4', 
        'C20', 'C20-1', 'C20-2', 'C20-3', 'C20-4', 
        'C21', 'C21-1', 'C21-2', 'C21-3', 'C21-4', 'C21-5', 
        'C22', 'C22-1', 'C22-2', 'C22-3', 'C22-4', 
        'C23', 'C23-1', 'C23-2', 'C23-3', 'C23-4', 'C23-5'
    ];
    
    echo "<h3>Inserting Floor 1 Tables with new naming scheme...</h3>";
    echo "<p>Total tables to insert: " . count($floor1TableNames) . "</p>";
    
    // Calculate positions for the tables
    $insertSql = "INSERT INTO kp_tables (table_id, x_position, y_position, width, height, status) VALUES (?, ?, ?, ?, ?, 'active')";
    $insertStmt = $conn->prepare($insertSql);
    
    $insertedCount = 0;
    $currentX = 100; // Starting X position
    $currentY = 50;  // Starting Y position
    $tableWidth = 50;
    $tableHeight = 30;
    $spacingX = 55;  // Horizontal spacing between tables
    $spacingY = 35;  // Vertical spacing between rows
    $tablesPerRow = 12; // Number of tables per row
    
    foreach ($floor1TableNames as $index => $tableName) {
        // Calculate position based on index
        $rowIndex = intval($index / $tablesPerRow);
        $colIndex = $index % $tablesPerRow;
        
        $x = $currentX + ($colIndex * $spacingX);
        $y = $currentY + ($rowIndex * $spacingY);
        
        // Special positioning for main tables (without dash) - make them slightly larger
        if (strpos($tableName, '-') === false) {
            $width = 60;
            $height = 35;
        } else {
            $width = $tableWidth;
            $height = $tableHeight;
        }
        
        // Insert the table
        $insertStmt->execute([$tableName, $x, $y, $width, $height]);
        $insertedCount++;
        
        // Show progress every 10 tables
        if ($insertedCount % 10 == 0) {
            echo "<p style='color: blue;'>✓ Inserted {$insertedCount} tables...</p>";
        }
    }
    
    echo "<p style='color: green;'>✅ Successfully inserted {$insertedCount} Floor 1 tables</p>";
    
    // Show sample of inserted data
    echo "<h3>Sample of inserted tables:</h3>";
    $sampleSql = "SELECT table_id, x_position, y_position, width, height FROM kp_tables WHERE table_id LIKE 'C%' ORDER BY table_id LIMIT 10";
    $sampleStmt = $conn->prepare($sampleSql);
    $sampleStmt->execute();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Table ID</th><th>X Position</th><th>Y Position</th><th>Width</th><th>Height</th></tr>";
    
    while ($row = $sampleStmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['table_id']) . "</td>";
        echo "<td>" . $row['x_position'] . "</td>";
        echo "<td>" . $row['y_position'] . "</td>";
        echo "<td>" . $row['width'] . "</td>";
        echo "<td>" . $row['height'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Final statistics
    $totalSql = "SELECT COUNT(*) as total FROM kp_tables";
    $totalStmt = $conn->prepare($totalSql);
    $totalStmt->execute();
    $totalCount = $totalStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    $floor1Sql = "SELECT COUNT(*) as floor1_total FROM kp_tables WHERE table_id LIKE 'C%'";
    $floor1Stmt = $conn->prepare($floor1Sql);
    $floor1Stmt->execute();
    $floor1Count = $floor1Stmt->fetch(PDO::FETCH_ASSOC)['floor1_total'];
    
    echo "<hr>";
    echo "<h3 style='color: green;'>✅ Floor 1 Update Complete!</h3>";
    echo "<ul>";
    echo "<li><strong>Floor 1 Tables:</strong> {$floor1Count}</li>";
    echo "<li><strong>Total Tables in Database:</strong> {$totalCount}</li>";
    echo "</ul>";
    
    echo "<h4>Table Name Pattern Summary:</h4>";
    echo "<ul>";
    echo "<li><strong>Main Tables:</strong> C1, C2, C3, ..., C23 (without dash)</li>";
    echo "<li><strong>Sub Tables:</strong> C1-1, C1-2, C2-1, C2-2, etc. (with dash)</li>";
    echo "<li><strong>Total Floor 1 Tables:</strong> {$floor1Count}</li>";
    echo "</ul>";
    
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='test_dynamic_tables.php' class='btn btn-primary' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>→ Test Dynamic Tables</a>";
    echo "<a href='mainpage.php' class='btn btn-secondary' style='background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>→ Main Floorplan</a>";
    echo "<a href='admin_dynamic_tables.php' class='btn btn-info' style='background: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>→ Admin Panel</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error</h3>";
    echo "<p style='color: red;'>An error occurred: " . $e->getMessage() . "</p>";
    echo "<p><a href='admin_dynamic_tables.php'>← Back to Admin Panel</a></p>";
}
?>
