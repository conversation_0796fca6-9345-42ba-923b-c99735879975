<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

include '../dbconnect/_dbconnect.php';

echo "<h2>Agent Search Function Test</h2>";

try {
    $conn = db_connect();
    
    // Check if table exists
    $sql = "SHOW TABLES LIKE 'kp_agent'";
    $result = $conn->query($sql);
    $tableExists = $result->rowCount() > 0;
    
    if (!$tableExists) {
        echo "<p style='color: red;'>❌ kp_agent table does not exist. Creating it...</p>";
        
        // Create the table
        $sql = "CREATE TABLE kp_agent (
            id INT(11) NOT NULL AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            contact_person VARCHAR(255),
            phone VARCHAR(20),
            email VARCHAR(255),
            address TEXT,
            status TINYINT(1) DEFAULT 1,
            create_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            update_date DATETIME ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        )";
        
        $conn->exec($sql);
        echo "<p style='color: green;'>✓ Table created successfully!</p>";
    } else {
        echo "<p style='color: green;'>✓ kp_agent table exists</p>";
    }
    
    // Check if there's any data
    $sql = "SELECT COUNT(*) as count FROM kp_agent";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "<p>Current number of agents: <strong>{$count}</strong></p>";
    
    // Add sample data if table is empty
    if ($count == 0) {
        echo "<h3>Adding Sample Data</h3>";
        
        $sampleAgents = [
            ['ABC Travel Agency', 'John Smith', '02-123-4567', '<EMAIL>', '123 Main St, Bangkok', 1],
            ['XYZ Tours', 'Jane Doe', '02-234-5678', '<EMAIL>', '456 Second St, Bangkok', 1],
            ['Golden Gate Travel', 'Mike Johnson', '02-345-6789', '<EMAIL>', '789 Third St, Bangkok', 1],
            ['Sunshine Tours', 'Sarah Wilson', '02-456-7890', '<EMAIL>', '321 Fourth St, Bangkok', 1],
            ['Dream Vacation', 'Tom Brown', '02-567-8901', '<EMAIL>', '654 Fifth St, Bangkok', 0]
        ];
        
        $sql = "INSERT INTO kp_agent (name, contact_person, phone, email, address, status) VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        
        foreach ($sampleAgents as $agent) {
            $stmt->execute($agent);
            echo "<p>✓ Added: {$agent[0]}</p>";
        }
        
        echo "<p style='color: green;'>✓ Sample data added successfully!</p>";
    }
    
    // Test search functionality
    echo "<h3>Testing Search Functionality</h3>";
    
    $searchTests = [
        '' => 'All agents (no search)',
        'ABC' => 'Search for "ABC"',
        'John' => 'Search for "John"',
        '02-123' => 'Search for phone "02-123"',
        'gmail' => 'Search for "gmail"',
        'nonexistent' => 'Search for non-existent term'
    ];
    
    foreach ($searchTests as $searchTerm => $description) {
        echo "<h4>{$description}</h4>";
        
        if (!empty($searchTerm)) {
            $sql = "SELECT * FROM kp_agent WHERE name LIKE ? OR contact_person LIKE ? OR phone LIKE ? OR email LIKE ? ORDER BY name ASC";
            $stmt = $conn->prepare($sql);
            $searchParam = "%{$searchTerm}%";
            $stmt->execute([$searchParam, $searchParam, $searchParam, $searchParam]);
        } else {
            $sql = "SELECT * FROM kp_agent ORDER BY name ASC";
            $stmt = $conn->prepare($sql);
            $stmt->execute();
        }

        $agents = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p><strong>Results:</strong> " . count($agents) . " agents found</p>";
        
        if (count($agents) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th>ID</th><th>Name</th><th>Contact Person</th><th>Phone</th><th>Email</th><th>Status</th>";
            echo "</tr>";
            
            foreach ($agents as $agent) {
                $statusText = $agent['status'] == 1 ? 'Active' : 'Inactive';
                $statusColor = $agent['status'] == 1 ? 'green' : 'red';
                
                echo "<tr>";
                echo "<td>{$agent['id']}</td>";
                echo "<td>{$agent['name']}</td>";
                echo "<td>{$agent['contact_person']}</td>";
                echo "<td>{$agent['phone']}</td>";
                echo "<td>{$agent['email']}</td>";
                echo "<td style='color: {$statusColor};'>{$statusText}</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p><em>No agents found.</em></p>";
        }
        
        echo "<hr>";
    }
    
    // Test API endpoints
    echo "<h3>Testing API Endpoints</h3>";
    
    $apiTests = [
        'agent_api.php' => 'Get all agents',
        'agent_api.php?search=ABC' => 'Search for "ABC"',
        'agent_api.php?search=John' => 'Search for "John"',
        'agent_api.php?id=1' => 'Get agent with ID 1'
    ];
    
    foreach ($apiTests as $endpoint => $description) {
        echo "<h4>{$description}</h4>";
        echo "<p><strong>Endpoint:</strong> <code>{$endpoint}</code></p>";
        
        $url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/" . $endpoint;
        $response = @file_get_contents($url);
        
        if ($response === false) {
            echo "<p style='color: red;'>❌ Failed to fetch data from API</p>";
        } else {
            $data = json_decode($response, true);
            
            if ($data === null) {
                echo "<p style='color: red;'>❌ Invalid JSON response</p>";
                echo "<pre>" . htmlspecialchars($response) . "</pre>";
            } else {
                if ($data['success']) {
                    echo "<p style='color: green;'>✅ API call successful</p>";
                    
                    if (isset($data['data'])) {
                        if (is_array($data['data']) && isset($data['data'][0])) {
                            // Multiple agents
                            echo "<p><strong>Results:</strong> " . count($data['data']) . " agents</p>";
                            if (count($data['data']) > 0) {
                                echo "<p><strong>First result:</strong> {$data['data'][0]['name']}</p>";
                            }
                        } elseif (is_array($data['data']) && isset($data['data']['name'])) {
                            // Single agent
                            echo "<p><strong>Agent:</strong> {$data['data']['name']}</p>";
                        }
                    }
                } else {
                    echo "<p style='color: red;'>❌ API error: {$data['message']}</p>";
                }
            }
        }
        
        echo "<hr>";
    }
    
    echo "<h3>Summary</h3>";
    echo "<p>✅ Agent table exists and has data</p>";
    echo "<p>✅ Search functionality is working correctly</p>";
    echo "<p>✅ API endpoints are responding properly</p>";
    
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='index.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>→ Go to Agent Management</a>";
    echo "<a href='debug_agent_api.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>→ Debug API</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

db_close($conn);
?>
