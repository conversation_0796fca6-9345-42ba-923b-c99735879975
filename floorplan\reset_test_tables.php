<?php
/**
 * Script to reset test tables back to normal status
 */

session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

include '../dbconnect/_dbconnect.php';

try {
    $conn = db_connect();
    
    echo "<h2>Resetting Test Tables</h2>";
    echo "<p>This script will reset all test tables back to normal active status.</p>";
    
    // Reset test tables
    $resetSql = "UPDATE kp_tables SET status = 'active', disabled_until = NULL WHERE table_id IN ('C1', 'C2', 'C3', 'C4', 'C5', 'C6')";
    $resetStmt = $conn->prepare($resetSql);
    $resetStmt->execute();
    
    $affectedRows = $resetStmt->rowCount();
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✅ Reset Complete!</h4>";
    echo "<p>Successfully reset {$affectedRows} test tables to active status.</p>";
    echo "</div>";
    
    // Show current status
    echo "<h3>Current Status:</h3>";
    $checkSql = "SELECT table_id, status, disabled_until FROM kp_tables WHERE table_id IN ('C1', 'C2', 'C3', 'C4', 'C5', 'C6') ORDER BY table_id";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->execute();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Table ID</th>";
    echo "<th style='padding: 8px;'>Status</th>";
    echo "<th style='padding: 8px;'>Disabled Until</th>";
    echo "</tr>";
    
    while ($row = $checkStmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr style='background: #d4edda;'>";
        echo "<td style='padding: 8px;'><strong>" . htmlspecialchars($row['table_id']) . "</strong></td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($row['status']) . "</td>";
        echo "<td style='padding: 8px;'>" . ($row['disabled_until'] ? htmlspecialchars($row['disabled_until']) : '<em>NULL</em>') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p>All test tables are now active and should appear normal in the floorplan.</p>";
    
    echo "<div style='margin-top: 30px;'>";
    echo "<a href='test_disabled_tables.php' style='background: #ffc107; color: #212529; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>→ Set Up Test Again</a>";
    echo "<a href='test_dynamic_tables.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>→ Test Dynamic Tables</a>";
    echo "<a href='mainpage.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>→ Main Floorplan</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error</h3>";
    echo "<p style='color: red;'>An error occurred: " . $e->getMessage() . "</p>";
}
?>
