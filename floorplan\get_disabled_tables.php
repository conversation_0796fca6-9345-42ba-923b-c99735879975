<?php
// Set error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set content type to JSON
header('Content-Type: application/json');

include '../dbconnect/_dbconnect.php';

try {
    // Connect to database
    $conn = db_connect();

    // Get date parameter from request (from myDate input field)
    $selectedDate = isset($_GET['useDate']) ? $_GET['useDate'] : date('Y-m-d');
    $selecteFloor = isset($_GET['useZone']) ? $_GET['useZone'] : '1';

    // Validate date format
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $selectedDate)) {
        $selectedDate = date('Y-m-d'); // Fallback to current date if invalid
    }

    // Query to get disabled tables based on the selected date
    // This gets tables that are either:
    // 1. Disabled with no end date (indefinitely disabled)
    // 2. Disabled with an end date that matches or is after the selected date
    // 3. Disabled with an end date that matches the selected date exactly
    $sql = "SELECT `table_id`, `status`, `disabled_until`, `floor` FROM `kp_tables`
            WHERE `status` = 'disabled'
            AND `floor` = :selecteFloor
            AND (
                `disabled_until` IS NULL
                OR `disabled_until` >= :selectedDate
                OR `disabled_until` = :selectedDate
            )";

    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':selectedDate', $selectedDate);
    $stmt->execute();
    
    // Fetch results
    $disabledTables = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $disabledTables[] = [
            'id' => $row['table_id'],
            'status' => $row['status'],
            'floor' => $row['floor'],
            'disabled_until' => $row['disabled_until'],
            'selected_date' => $selectedDate // Include the date being checked
        ];
    }

    // Return disabled tables as JSON with metadata
    echo json_encode([
        'status' => 'success',
        'selected_date' => $selectedDate,
        'count' => count($disabledTables),
        'tables' => $disabledTables
    ]);
    
} catch (Exception $e) {
    // Return error response
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
