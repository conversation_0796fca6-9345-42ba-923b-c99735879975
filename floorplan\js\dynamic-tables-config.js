/**
 * Dynamic Tables Configuration
 * 
 * This file contains configuration options for the dynamic table system.
 * Include this file before dynamic-tables.js to configure the system.
 */

// Configuration options
window.DYNAMIC_TABLES_CONFIG = {
    // Enable or disable the dynamic table system
    enabled: false, // Set to true to enable dynamic tables
    
    // Replace hardcoded SVG tables with dynamic ones
    replaceHardcoded: false, // Set to true to hide hardcoded tables
    
    // Debug mode - shows additional console logging
    debug: false,
    
    // Default table colors
    colors: {
        available: '#539bff',    // Blue - available table
        booked: '#dc3545',       // Red - booked table  
        selected: '#28a745',     // Green - selected table
        disabled: '#6c757d',     // Gray - disabled table
        vip: '#12deb9'          // Teal - VIP room
    },
    
    // Default table dimensions
    defaultTable: {
        width: 50,
        height: 30,
        fontSize: 14
    },
    
    // API endpoints
    endpoints: {
        getTables: 'get_tables.php',
        getBookedTables: 'get_booked_tables.php',
        getDisabledTables: 'get_disabled_tables.php'
    }
};

// Set global flags for backward compatibility
window.ENABLE_DYNAMIC_TABLES = window.DYNAMIC_TABLES_CONFIG.enabled;
window.REPLACE_HARDCODED_TABLES = window.DYNAMIC_TABLES_CONFIG.replaceHardcoded;
