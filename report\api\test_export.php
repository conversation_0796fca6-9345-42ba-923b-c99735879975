<?php
/**
 * Test Export API
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Simple test without authentication for debugging
echo "Session status: " . (isset($_SESSION['loggedin']) ? 'logged in' : 'not logged in') . "\n";
echo "Parameters: " . print_r($_GET, true) . "\n";

// Test database connection
try {
    require_once '../../dbconnect/_dbconnect.php';
    $conn = db_connect();
    
    if ($conn) {
        echo "Database connection: SUCCESS\n";
        
        // Test simple query
        $stmt = $conn->query("SELECT COUNT(*) as count FROM kp_booking");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Total bookings in database: " . $result['count'] . "\n";
        
    } else {
        echo "Database connection: FAILED\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

// Test Excel headers
if (isset($_GET['test_excel'])) {
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="test.xls"');
    header('Cache-Control: max-age=0');
    
    echo '<html>';
    echo '<body>';
    echo '<table border="1">';
    echo '<tr><th>Test</th><th>Value</th></tr>';
    echo '<tr><td>Hello</td><td>World</td></tr>';
    echo '</table>';
    echo '</body>';
    echo '</html>';
}
?>
