<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <!-- Set maximum request length for IIS -->
        <security>
            <requestFiltering>
                <!-- 25MB = 26214400 bytes -->
                <requestLimits maxAllowedContentLength="26214400" />
            </requestFiltering>
        </security>
        
        <!-- PHP configuration -->
        <fastCgi>
            <application fullPath="*">
                <environmentVariables>
                    <environmentVariable name="PHP_FCGI_MAX_REQUESTS" value="10000" />
                </environmentVariables>
            </application>
        </fastCgi>
    </system.webServer>
    
    <system.web>
        <!-- Set maximum request length for ASP.NET (in KB) -->
        <!-- 25MB = 25600 KB -->
        <httpRuntime maxRequestLength="25600" executionTimeout="300" />
    </system.web>
</configuration>
