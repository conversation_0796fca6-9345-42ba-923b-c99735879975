<?php
session_start();
require_once '../../dbconnect/_dbconnect.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors to user
ini_set('log_errors', 1);

// Set content type to JSON
header('Content-Type: application/json');

// Log the request for debugging
error_log("=== Payment Image Upload Request ===");
error_log("POST data: " . print_r($_POST, true));
error_log("FILES data: " . print_r($_FILES, true));
error_log("Session data: " . print_r($_SESSION, true));

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Check if request was rejected by web server
if (empty($_POST) && empty($_FILES) && $_SERVER['CONTENT_LENGTH'] > 0) {
    http_response_code(413);
    echo json_encode([
        'success' => false, 
        'message' => 'File too large for web server. Please reduce file size to under 5MB.',
        'content_length' => $_SERVER['CONTENT_LENGTH']
    ]);
    exit;
}

// Validate required fields
if (!isset($_POST['booking_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing booking_id']);
    exit;
}

$bookingId = intval($_POST['booking_id']);

// Handle single file upload
if (!isset($_FILES['payment_image']) || $_FILES['payment_image']['error'] !== UPLOAD_ERR_OK) {
    $error = $_FILES['payment_image']['error'] ?? 'No file uploaded';
    
    $errorMessages = [
        UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize directive',
        UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE directive',
        UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
        UPLOAD_ERR_NO_FILE => 'No file was uploaded',
        UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
        UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
        UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
    ];
    
    $errorMessage = $errorMessages[$error] ?? 'Unknown upload error';
    
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $errorMessage]);
    exit;
}

$file = $_FILES['payment_image'];
$allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
$maxSize = 2 * 1024 * 1024; // 2MB per file (reduced for nginx compatibility)

// Validate file type
if (!in_array($file['type'], $allowedTypes)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, and GIF are allowed.']);
    exit;
}

// Validate file size
if ($file['size'] > $maxSize) {
    http_response_code(413);
    echo json_encode(['success' => false, 'message' => 'File is too large. Maximum size is 2MB.']);
    exit;
}

try {
    // Create upload directory if it doesn't exist
    $uploadDir = '../../uploads/payment_references/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // Generate unique filename
    $fileExtension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $fileName = 'payment_' . $bookingId . '_' . time() . '_' . uniqid() . '.' . $fileExtension;
    $filePath = $uploadDir . $fileName;

    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $filePath)) {
        throw new Exception('Failed to move uploaded file');
    }

    // Just return the file path - let the main API handle database updates
    $imagePath = '../uploads/payment_references/' . $fileName;

    echo json_encode([
        'success' => true,
        'message' => 'Image uploaded successfully',
        'image_path' => $imagePath,
        'file_name' => $fileName
    ]);

} catch (Exception $e) {
    error_log("Payment image upload error: " . $e->getMessage());

    // Clean up uploaded file if upload failed
    if (isset($filePath) && file_exists($filePath)) {
        unlink($filePath);
    }

    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Failed to upload image: ' . $e->getMessage()]);
}
?>
