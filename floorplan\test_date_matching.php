<?php
/**
 * Test Date Matching Logic for Disabled Tables
 */

// Start session
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('location:../login');
    exit;
}

include '../dbconnect/_dbconnect.php';

echo "<h2>Testing Date Matching Logic for Disabled Tables</h2>";

try {
    $conn = db_connect();
    
    // Create test data with different disabled_until scenarios
    $testDate = '2025-07-26';
    $testZone = '1';
    
    echo "<p><strong>Test Date:</strong> {$testDate}</p>";
    echo "<p><strong>Test Zone:</strong> {$testZone}</p>";
    
    // Clear existing test data
    $conn->exec("DELETE FROM kp_tables WHERE table_id LIKE 'TEST%'");
    
    // Insert test scenarios
    $testScenarios = [
        ['TEST1', '1', 'disabled', '2025-07-26'], // Should be hidden (matches selected date)
        ['TEST2', '1', 'disabled', '2025-07-27'], // Should NOT be hidden (after selected date - table available)
        ['TEST3', '1', 'disabled', '2025-07-25'], // Should NOT be hidden (before selected date - table available)
        ['TEST4', '1', 'disabled', null],         // Should be hidden (permanently disabled)
        ['TEST5', '1', 'active', '2025-07-26'],   // Should NOT appear (not disabled)
    ];
    
    $insertStmt = $conn->prepare("INSERT INTO kp_tables (table_id, floor, status, disabled_until) VALUES (?, ?, ?, ?)");
    
    echo "<h3>Test Scenarios:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Table ID</th><th>Floor</th><th>Status</th><th>Disabled Until</th><th>Expected Result</th></tr>";
    
    foreach ($testScenarios as $scenario) {
        $insertStmt->execute($scenario);
        
        $expectedResult = '';
        if ($scenario[2] === 'active') {
            $expectedResult = 'Not in API response (active)';
        } elseif ($scenario[3] === null) {
            $expectedResult = 'Hidden (permanently disabled)';
        } elseif ($scenario[3] === $testDate) {
            $expectedResult = 'Hidden (matches selected date)';
        } elseif ($scenario[3] > $testDate) {
            $expectedResult = 'NOT hidden (after selected date - table available)';
        } else {
            $expectedResult = 'NOT hidden (before selected date - table available)';
        }
        
        echo "<tr>";
        echo "<td>{$scenario[0]}</td>";
        echo "<td>{$scenario[1]}</td>";
        echo "<td>{$scenario[2]}</td>";
        echo "<td>" . ($scenario[3] ?: 'NULL (permanent)') . "</td>";
        echo "<td><strong>{$expectedResult}</strong></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test the API
    echo "<h3>API Response Test:</h3>";
    $selectedDate = $testDate;
    $selectedFloor = $testZone;
    
    $sql = "SELECT `table_id`, `status`, `disabled_until`, `floor` FROM `kp_tables`
            WHERE `status` = 'disabled'
            AND `floor` = :selectedFloor";

    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':selectedFloor', $selectedFloor);
    $stmt->execute();
    
    $disabledTables = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $disabledTables[] = [
            'id' => $row['table_id'],
            'status' => $row['status'],
            'floor' => $row['floor'],
            'disabled_until' => $row['disabled_until'],
            'selected_date' => $selectedDate
        ];
    }
    
    $response = [
        'status' => 'success',
        'selected_date' => $selectedDate,
        'selected_floor' => $selectedFloor,
        'count' => count($disabledTables),
        'tables' => $disabledTables
    ];
    
    echo "<h4>Raw API Response:</h4>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
    echo json_encode($response, JSON_PRETTY_PRINT);
    echo "</pre>";
    
    // Simulate the JavaScript filtering logic
    echo "<h3>JavaScript Filtering Simulation:</h3>";
    
    $tablesToHide = [];
    foreach ($disabledTables as $table) {
        $shouldHide = false;
        $reason = '';
        
        if (!$table['disabled_until']) {
            // Permanently disabled
            $shouldHide = true;
            $reason = 'Permanently disabled (disabled_until is NULL)';
        } elseif ($table['disabled_until'] === $table['selected_date']) {
            // Matches selected date
            $shouldHide = true;
            $reason = "disabled_until ({$table['disabled_until']}) matches selected_date ({$table['selected_date']})";
        } elseif ($table['disabled_until'] > $table['selected_date']) {
            // After selected date - table is available
            $shouldHide = false;
            $reason = "disabled_until ({$table['disabled_until']}) is after selected_date ({$table['selected_date']}) - table is available";
        } else {
            // Before selected date - table is available
            $shouldHide = false;
            $reason = "disabled_until ({$table['disabled_until']}) is before selected_date ({$table['selected_date']}) - table is available";
        }
        
        if ($shouldHide) {
            $tablesToHide[] = $table;
        }
        
        echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>";
        echo "<strong>Table {$table['id']}:</strong> ";
        echo "<span style='color: " . ($shouldHide ? 'red' : 'green') . ";'>";
        echo $shouldHide ? '✅ HIDE' : '❌ SHOW';
        echo "</span>";
        echo "<br><em>Reason: {$reason}</em>";
        echo "</div>";
    }
    
    echo "<h4>Summary:</h4>";
    echo "<p><strong>Total disabled tables from API:</strong> " . count($disabledTables) . "</p>";
    echo "<p><strong>Tables to hide:</strong> " . count($tablesToHide) . "</p>";
    echo "<p><strong>Tables to show:</strong> " . (count($disabledTables) - count($tablesToHide)) . "</p>";
    
    // Clean up test data
    $conn->exec("DELETE FROM kp_tables WHERE table_id LIKE 'TEST%'");
    echo "<p><em>Test data cleaned up.</em></p>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Error: " . $e->getMessage() . "</div>";
}

echo "<hr>";
echo "<h3>Test the Updated JavaScript Logic:</h3>";
echo "<button onclick='testUpdatedLogic()'>Test Updated Logic</button>";
echo "<div id='js-test-results'></div>";

?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function testUpdatedLogic() {
    console.log('Testing updated JavaScript logic...');
    
    // Test with the actual API
    $.ajax({
        url: 'get_disabled_tables.php',
        type: 'GET',
        data: { 
            useZone: '1',
            useDate: '2025-07-26'
        },
        dataType: 'json',
        success: function(response) {
            console.log('✅ API Response:', response);
            
            // Simulate the updated handleDisabledTablesSuccess logic
            let disabled = [];
            let selectedDate = '';
            
            if (response && response.status === 'success' && Array.isArray(response.tables)) {
                disabled = response.tables;
                selectedDate = response.selected_date;
            }
            
            // Filter tables based on the condition
            const tablesToHide = disabled.filter(table => {
                if (!table || !table.disabled_until) {
                    return true; // Permanently disabled
                }
                
                const disabledUntil = table.disabled_until;
                const tableSelectedDate = table.selected_date || selectedDate;
                
                // Hide if disabled_until matches selected_date
                if (disabledUntil === tableSelectedDate) {
                    return true;
                }

                // Do NOT hide if disabled_until is in the future (table is available)
                if (disabledUntil > tableSelectedDate) {
                    return false;
                }

                // Do NOT hide if disabled_until is in the past (table is available)
                return false;
            });
            
            let resultHtml = '<h4>JavaScript Test Results:</h4>';
            resultHtml += `<p>Total disabled tables: ${disabled.length}</p>`;
            resultHtml += `<p>Tables to hide: ${tablesToHide.length}</p>`;
            
            if (tablesToHide.length > 0) {
                resultHtml += '<h5>Tables that will be hidden:</h5><ul>';
                tablesToHide.forEach(table => {
                    const reason = !table.disabled_until ? 'Permanently disabled' :
                                 table.disabled_until === selectedDate ? 'Matches selected date' :
                                 'Unknown reason';
                    resultHtml += `<li>Table ${table.id} - ${reason}</li>`;
                });
                resultHtml += '</ul>';
            }
            
            $('#js-test-results').html(resultHtml);
        },
        error: function(xhr, status, error) {
            console.error('❌ API Error:', error);
            $('#js-test-results').html(`<p style="color: red;">Error: ${error}</p>`);
        }
    });
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
button { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; }
button:hover { background: #005a87; }
</style>
