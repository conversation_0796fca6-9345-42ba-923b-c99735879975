<?php
/**
 * <PERSON><PERSON><PERSON> to update Floor 3 tables in the database
 * This script will add the new Floor 3 table configuration
 */

session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

include '../dbconnect/_dbconnect.php';

try {
    // Connect to database
    $conn = db_connect();
    
    // Define the new Floor 3 tables
    $floor3Tables = [
        'A1', 'A1-1', 'A1-2', 'A1-3', 'A1-4', 'A1-5',
        'A2', 'A2-1', 'A2-2', 'A2-3', 'A2-4', 'A2-5', 'A2-6',
        'A3', 'A3-1', 'A3-2', 'A3-3', 'A3-4', 'A3-5', 'A3-6',
        'A4', 'A4-1', 'A4-2', 'A4-3', 'A4-4', 'A4-5', 'A4-6',
        'A5', 'A5-1', 'A5-2', 'A5-3', 'A5-4', 'A5-5', 'A5-6',
        'A6', 'A6-1', 'A6-2', 'A6-3', 'A6-4', 'A6-5', 'A6-6',
        'A7', 'A7-1', 'A7-2', 'A7-3', 'A7-4', 'A7-5', 'A7-6',
        'A8', 'A8-1', 'A8-2', 'A8-3', 'A8-4', 'A8-5', 'A8-6',
        'A9', 'A9-1', 'A9-2', 'A9-3', 'A9-4', 'A9-5', 'A9-6',
        'A10', 'A10-1', 'A10-2', 'A10-3', 'A10-4', 'A10-5',
        'A11', 'A11-1', 'A11-2', 'A11-3', 'A11-4', 'A11-5',
        'A12', 'A12-1', 'A12-2', 'A12-3', 'A12-4', 'A12-5',
        'A14', 'A14-1', 'A14-2', 'A14-3', 'A14-4', 'A14-5',
        'A15', 'A15-1', 'A15-2', 'A15-3', 'A15-4',
        'A16', 'A16-1', 'A16-2', 'A16-3', 'A16-4',
        'A17', 'A17-1', 'A17-2', 'A17-3',
        'A18', 'A18-1', 'A18-2',
        'A19', 'A19-1', 'A19-2', 'A19-3',
        'A20', 'A20-1', 'A20-2', 'A20-3', 'A20-4', 'A20-5',
        'A21', 'A21-1', 'A21-2', 'A21-3', 'A21-4', 'A21-5',
        'A22', 'A22-1', 'A22-2', 'A22-3', 'A22-4', 'A22-5',
        'A23', 'A23-1', 'A23-2', 'A23-3', 'A23-4', 'A23-5',
        'A24', 'A24-1', 'A24-2', 'A24-3', 'A24-4', 'A24-5',
        'A25', 'A25-1', 'A25-2', 'A25-3', 'A25-4', 'A25-5',
        'A26', 'A26-1', 'A26-2', 'A26-3', 'A26-4', 'A26-5',
        'A27', 'A27-1', 'A27-2', 'A27-3', 'A27-4', 'A27-5',
        'A28', 'A28-1', 'A28-2', 'A28-3', 'A28-4', 'A28-5',
        'A29', 'A29-1', 'A29-2', 'A29-3', 'A29-4', 'A29-5',
        'A30', 'A30-1', 'A30-2', 'A30-3', 'A30-4', 'A30-5',
        'A31', 'A31-1', 'A31-2', 'A31-3', 'A31-4', 'A31-5',
        'A32', 'A32-1', 'A32-2', 'A32-3', 'A32-4', 'A32-5',
        'A33', 'A33-1', 'A33-2', 'A33-3', 'A33-4',
        'A34', 'A34-1', 'A34-2', 'A34-3',
        'A35', 'A35-1', 'A35-2',
        'A36', 'A36-1', 'A36-2'
    ];
    
    echo "<h2>Updating Floor 3 Tables</h2>";
    echo "<p>Total tables to process: " . count($floor3Tables) . "</p>";
    
    // First, remove old Floor 3 tables (C1-C12 format)
    echo "<h3>Removing old Floor 3 tables...</h3>";
    $deleteOldSql = "DELETE FROM kp_tables WHERE 
        table_id LIKE 'C1/%' OR table_id LIKE 'C2/%' OR table_id LIKE 'C3/%' OR table_id LIKE 'C4/%' OR 
        table_id LIKE 'C5/%' OR table_id LIKE 'C6/%' OR table_id LIKE 'C7/%' OR table_id LIKE 'C8/%' OR 
        table_id LIKE 'C9/%' OR table_id LIKE 'C10/%' OR table_id LIKE 'C11/%' OR table_id LIKE 'C12/%'";
    $deleteStmt = $conn->prepare($deleteOldSql);
    $deleteStmt->execute();
    $deletedCount = $deleteStmt->rowCount();
    echo "<p>Removed $deletedCount old Floor 3 tables.</p>";
    
    // Insert new Floor 3 tables
    echo "<h3>Adding new Floor 3 tables...</h3>";
    $insertSql = "INSERT IGNORE INTO kp_tables (table_id, status, x_position, y_position, width, height, disabled_until) VALUES (?, 'active', 0, 0, 50, 30, NULL)";
    $insertStmt = $conn->prepare($insertSql);
    
    $addedCount = 0;
    $skippedCount = 0;
    
    foreach ($floor3Tables as $index => $tableId) {
        try {
            $insertStmt->execute([$tableId]);
            if ($insertStmt->rowCount() > 0) {
                $addedCount++;
                echo "<span style='color: green;'>✓</span> Added table: $tableId<br>";
            } else {
                $skippedCount++;
                echo "<span style='color: orange;'>⚠</span> Skipped (already exists): $tableId<br>";
            }
        } catch (Exception $e) {
            echo "<span style='color: red;'>✗</span> Error adding table $tableId: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<h3>Summary</h3>";
    echo "<ul>";
    echo "<li>Tables added: <strong>$addedCount</strong></li>";
    echo "<li>Tables skipped (already existed): <strong>$skippedCount</strong></li>";
    echo "<li>Old tables removed: <strong>$deletedCount</strong></li>";
    echo "</ul>";
    
    // Verify the update
    echo "<h3>Verification</h3>";
    $verifySql = "SELECT COUNT(*) as count FROM kp_tables WHERE table_id IN ('" . implode("','", $floor3Tables) . "')";
    $verifyStmt = $conn->prepare($verifySql);
    $verifyStmt->execute();
    $verifyResult = $verifyStmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p>Current Floor 3 tables in database: <strong>" . $verifyResult['count'] . "</strong></p>";
    
    if ($verifyResult['count'] == count($floor3Tables)) {
        echo "<p style='color: green; font-weight: bold;'>✅ All Floor 3 tables have been successfully updated!</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ Some tables may be missing. Please check the database.</p>";
    }
    
    echo "<hr>";
    echo "<p><a href='index.php'>← Back to Table Setup</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error</h3>";
    echo "<p>An error occurred: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.php'>← Back to Table Setup</a></p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Floor 3 Tables Update</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h2, h3 { color: #333; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <!-- Content is generated above -->
</body>
</html>
