<?php
/**
 * <PERSON><PERSON><PERSON> to create the kp_tables table structure
 * This script will create the necessary table structure for the table setup system
 */

session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

include '../dbconnect/_dbconnect.php';

try {
    // Connect to database
    $conn = db_connect();
    
    echo "<h2>Creating kp_tables Table Structure</h2>";
    
    // Drop table if exists (optional - for clean setup)
    echo "<h3>Dropping existing table (if exists)...</h3>";
    $dropSql = "DROP TABLE IF EXISTS kp_tables";
    $dropStmt = $conn->prepare($dropSql);
    $dropStmt->execute();
    echo "<p style='color: orange;'>✓ Dropped existing kp_tables table (if it existed)</p>";
    
    // Create kp_tables table
    echo "<h3>Creating kp_tables table...</h3>";
    $createSql = "
        CREATE TABLE kp_tables (
            id INT AUTO_INCREMENT PRIMARY KEY,
            table_id VARCHAR(20) NOT NULL UNIQUE,
            status ENUM('active', 'disabled') DEFAULT 'active',
            x_position INT DEFAULT 0,
            y_position INT DEFAULT 0,
            width INT DEFAULT 50,
            height INT DEFAULT 30,
            disabled_until DATE NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_table_id (table_id),
            INDEX idx_status (status),
            INDEX idx_disabled_until (disabled_until)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $createStmt = $conn->prepare($createSql);
    $createStmt->execute();
    echo "<p style='color: green; font-weight: bold;'>✅ Successfully created kp_tables table!</p>";
    
    // Verify table creation
    echo "<h3>Verifying table structure...</h3>";
    $describeSql = "DESCRIBE kp_tables";
    $describeStmt = $conn->prepare($describeSql);
    $describeStmt->execute();
    $columns = $describeStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; background-color: white;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th>";
    echo "</tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Table Structure Details</h3>";
    echo "<ul>";
    echo "<li><strong>id:</strong> Auto-incrementing primary key</li>";
    echo "<li><strong>table_id:</strong> Unique table identifier (e.g., VIP1, C1-1, H2-3, A5-2)</li>";
    echo "<li><strong>status:</strong> Table status (active/disabled)</li>";
    echo "<li><strong>x_position, y_position:</strong> Table coordinates for floor plan</li>";
    echo "<li><strong>width, height:</strong> Table dimensions</li>";
    echo "<li><strong>disabled_until:</strong> Date until which table is disabled (NULL = indefinitely or not disabled)</li>";
    echo "<li><strong>created_at, updated_at:</strong> Timestamp tracking</li>";
    echo "</ul>";
    
    echo "<p style='color: green; font-weight: bold; font-size: 18px;'>✅ Database table is ready for table insertion!</p>";
    
    echo "<hr>";
    echo "<p>";
    echo "<a href='insert_all_tables.php' style='background-color: #28a745; margin-right: 10px;'>→ Insert All Tables</a>";
    echo "<a href='index.php'>← Back to Table Setup</a>";
    echo "</p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error</h3>";
    echo "<p style='color: red;'>An error occurred: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.php'>← Back to Table Setup</a></p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Create kp_tables Table</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }
        h2, h3 { 
            color: #333; 
        }
        h2 {
            background-color: #007bff;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        h3 {
            background-color: #6c757d;
            color: white;
            padding: 10px;
            border-radius: 3px;
            margin-top: 20px;
        }
        ul, p {
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 10px 0;
        }
        table {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 10px 0;
        }
        a {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 10px;
        }
        a:hover {
            background-color: #0056b3;
        }
        hr {
            border: none;
            height: 2px;
            background-color: #dee2e6;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- Content is generated above -->
</body>
</html>
