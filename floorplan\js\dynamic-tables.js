/**
 * Dynamic Table Rendering System
 * This script dynamically generates SVG table elements from database data
 * instead of using hardcoded SVG elements in the HTML
 */

class DynamicTableRenderer {
    constructor(options = {}) {
        this.currentFloor = 1;
        this.bookedTables = [];
        this.disabledTables = [];
        this.selectedTables = [];
        this.currentTables = []; // Store current table data for status checking
        this.enabled = options.enabled !== false; // Default to enabled
        this.replaceHardcoded = options.replaceHardcoded || false; // Default to false
    }

    /**
     * Initialize the dynamic table system
     */
    init() {
        if (!this.enabled) {
            console.log('Dynamic Table Renderer is disabled');
            return;
        }

        console.log('Initializing Dynamic Table Renderer');

        if (this.replaceHardcoded) {
            this.hideHardcodedTables();
        }

        this.loadTablesForCurrentFloor();
        this.setupEventListeners();
    }

    /**
     * Hide hardcoded table elements if replacement is enabled
     */
    hideHardcodedTables() {
        console.log('Hiding hardcoded table elements');
        $('svg a[id^="box-"]:not([id*="VIP"])').hide();
    }

    /**
     * Setup event listeners for floor changes and date changes
     */
    setupEventListeners() {
        // Listen for floor tab changes
        $('a[data-bs-toggle="tab"]').on('shown.bs.tab', (e) => {
            const target = $(e.target).attr('href');
            if (target === '#floor1') {
                this.currentFloor = 1;
            } else if (target === '#floor2') {
                this.currentFloor = 2;
            } else if (target === '#floor3') {
                this.currentFloor = 3;
            }
            this.loadTablesForCurrentFloor();
        });

        // Listen for date changes
        $('#myDate').on('change', () => {
            this.loadBookingData();
            // Refresh table appearances when date changes (for disabled_until logic)
            this.updateAllTableAppearances();
        });
    }

    /**
     * Load tables for the current floor
     */
    loadTablesForCurrentFloor() {
        console.log(`Loading tables for floor ${this.currentFloor}`);

        $.ajax({
            url: 'get_tables.php',
            type: 'GET',
            data: {
                floor: this.currentFloor,
                status: 'all'
            },
            dataType: 'json',
            success: (data) => {
                if (data.error) {
                    console.error('Error loading tables:', data.error);
                    this.showNoTablesMessage();
                    return;
                }

                if (!Array.isArray(data) || data.length === 0) {
                    console.warn(`No tables found for floor ${this.currentFloor}`);
                    this.showNoTablesMessage();
                    return;
                }

                console.log(`Found ${data.length} tables for floor ${this.currentFloor}`);
                this.renderTables(data);
                this.loadBookingData();
            },
            error: (xhr, status, error) => {
                console.error('AJAX error loading tables:', error);
                this.showNoTablesMessage();
            }
        });
    }

    /**
     * Show message when no tables are found
     */
    showNoTablesMessage() {
        const svgContainer = this.getSvgContainer();
        if (!svgContainer) return;

        this.clearDynamicTables(svgContainer);

        // Add a text message to the SVG
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('x', '600');
        text.setAttribute('y', '200');
        text.setAttribute('font-size', '18');
        text.setAttribute('fill', '#dc3545');
        text.setAttribute('text-anchor', 'middle');
        text.setAttribute('font-weight', 'bold');
        text.setAttribute('id', 'no-tables-message');
        text.textContent = `No tables found for Floor ${this.currentFloor}. Please populate the database first.`;

        svgContainer.appendChild(text);
    }

    /**
     * Render SVG table elements from database data
     */
    renderTables(tables) {
        const svgContainer = this.getSvgContainer();
        if (!svgContainer) {
            console.error('SVG container not found for floor', this.currentFloor);
            return;
        }

        // Store current tables data for status checking
        this.currentTables = tables;

        // Clear existing dynamic tables (keep background image and VIP rooms)
        this.clearDynamicTables(svgContainer);

        // Group tables by row for better organization
        const tablesByRow = this.groupTablesByRow(tables);

        // Render each table
        tables.forEach(table => {
            if (this.isVipRoom(table.id)) {
                // Skip VIP rooms as they are handled separately
                return;
            }
            this.renderSingleTable(svgContainer, table);
        });

        console.log(`Rendered ${tables.length} tables for floor ${this.currentFloor}`);
    }

    /**
     * Get the SVG container for the current floor
     */
    getSvgContainer() {
        const floorId = `#floor${this.currentFloor}`;
        return $(floorId).find('svg')[0];
    }

    /**
     * Clear existing dynamic tables from SVG
     */
    clearDynamicTables(svgContainer) {
        // Remove all table elements that were dynamically created
        $(svgContainer).find('a[id^="box-"]:not([id*="VIP"])').remove();
        // Remove no-tables message if it exists
        $(svgContainer).find('#no-tables-message').remove();
    }

    /**
     * Group tables by row identifier
     */
    groupTablesByRow(tables) {
        const grouped = {};
        tables.forEach(table => {
            let row = table.row || 'unknown';

            // Handle new Floor 1 naming: C1, C1-1, C1-2 should all be grouped under C1
            if (table.id && table.id.includes('-')) {
                row = table.id.split('-')[0];
            }

            if (!grouped[row]) {
                grouped[row] = [];
            }
            grouped[row].push(table);
        });
        return grouped;
    }

    /**
     * Check if table is a VIP room
     */
    isVipRoom(tableId) {
        return tableId.includes('VIP');
    }

    /**
     * Render a single table element
     */
    renderSingleTable(svgContainer, table) {
        // Create the anchor element
        const anchor = document.createElementNS('http://www.w3.org/2000/svg', 'a');
        anchor.setAttribute('href', 'javascript:;');
        anchor.setAttribute('id', `box-${table.id}`);

        // Set CSS classes based on table status
        let cssClasses = 'svg-box';
        if (this.isTableDisabled(table)) {
            cssClasses += ' table-disabled';
        }
        anchor.setAttribute('class', cssClasses);
        anchor.setAttribute('data-value', '0');

        // Create the rectangle
        const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        rect.setAttribute('id', `rbox-${table.id}`);
        rect.setAttribute('x', table.x);
        rect.setAttribute('y', table.y);
        rect.setAttribute('width', table.width);
        rect.setAttribute('height', table.height);
        rect.setAttribute('fill', this.getTableColor(table));
        rect.setAttribute('stroke', '#ffffff');
        rect.setAttribute('stroke-width', '2');
        rect.setAttribute('rx', '5');
        rect.setAttribute('ry', '5');

        // Create the text element
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('x', table.x + (table.width / 2));
        text.setAttribute('y', table.y + (table.height / 2) + 6); // +6 for vertical centering
        text.setAttribute('font-size', this.getFontSize(table.width));
        text.setAttribute('fill', '#ffffff');
        text.setAttribute('text-anchor', 'middle');
        text.setAttribute('font-weight', 'bold');
        text.textContent = table.id;

        // Append elements
        anchor.appendChild(rect);
        anchor.appendChild(text);
        svgContainer.appendChild(anchor);

        // Add click event listener (disabled tables should not be clickable)
        if (!this.isTableDisabled(table)) {
            $(anchor).on('click', () => {
                this.handleTableClick(table.id);
            });
        } else {
            // Add disabled styling and tooltip
            $(anchor).css('cursor', 'not-allowed');
            $(anchor).attr('title', `Table ${table.id} is disabled${table.disabled_until ? ' until ' + table.disabled_until : ''}`);
        }
    }

    /**
     * Get appropriate color for table based on status
     */
    getTableColor(table) {
        // Check if table should be disabled based on status and disabled_until
        if (this.isTableDisabled(table)) {
            return '#6c757d'; // Gray for disabled
        }
        if (this.bookedTables.includes(table.id)) {
            return '#dc3545'; // Red for booked
        }
        if (this.selectedTables.includes(table.id)) {
            return '#28a745'; // Green for selected
        }
        return '#539bff'; // Default blue
    }

    /**
     * Check if table should be disabled based on status and disabled_until fields
     */
    isTableDisabled(table) {
        const currentDate = this.getCurrentDate();

        // Condition 1: status = active and disabled_until = blank/null -> display normally (not disabled)
        if (table.status === 'active' && (!table.disabled_until || table.disabled_until === '')) {
            return false;
        }

        // Condition 2: status = disabled and disabled_until matches current date -> disabled
        if (table.status === 'disabled' && table.disabled_until && table.disabled_until === currentDate) {
            return true;
        }

        // Condition 3: status = disabled and disabled_until is blank/null -> disabled
        if (table.status === 'disabled' && (!table.disabled_until || table.disabled_until === '')) {
            return true;
        }

        // For other cases (e.g., disabled with future date), check if current date is within disabled period
        if (table.status === 'disabled' && table.disabled_until) {
            return currentDate <= table.disabled_until;
        }

        return false;
    }

    /**
     * Get current date from the date input or today's date
     */
    getCurrentDate() {
        const dateInput = $('#myDate').val();
        if (dateInput) {
            return dateInput;
        }

        // Return today's date in YYYY-MM-DD format
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    /**
     * Get appropriate font size based on table width
     */
    getFontSize(width) {
        if (width < 50) return '12';
        if (width < 70) return '14';
        return '17';
    }

    /**
     * Handle table click events
     */
    handleTableClick(tableId) {
        console.log('Table clicked:', tableId);

        // Find the table data to check if it's disabled
        const tableData = this.currentTables ? this.currentTables.find(t => t.id === tableId) : null;
        if (tableData && this.isTableDisabled(tableData)) {
            const disabledMessage = tableData.disabled_until
                ? `Table ${tableId} is disabled until ${tableData.disabled_until}`
                : `Table ${tableId} is disabled`;
            alert(disabledMessage);
            return;
        }

        // Check if table is booked
        if (this.bookedTables.includes(tableId)) {
            alert(`Table ${tableId} is already booked`);
            return;
        }

        // Toggle selection
        const index = this.selectedTables.indexOf(tableId);
        if (index > -1) {
            this.selectedTables.splice(index, 1);
        } else {
            this.selectedTables.push(tableId);
        }

        // Update table appearance
        this.updateTableAppearance(tableId);

        // Trigger custom event for other components
        $(document).trigger('tableSelectionChanged', {
            tableId: tableId,
            selectedTables: this.selectedTables
        });
    }

    /**
     * Update table appearance based on current state
     */
    updateTableAppearance(tableId) {
        const rect = $(`#rbox-${tableId}`);
        const anchor = $(`#box-${tableId}`);

        if (rect.length && anchor.length) {
            // Find the table data
            const tableData = this.currentTables ? this.currentTables.find(t => t.id === tableId) : null;

            if (tableData) {
                // Update color
                rect.attr('fill', this.getTableColor(tableData));

                // Update CSS classes
                let cssClasses = 'svg-box';
                if (this.isTableDisabled(tableData)) {
                    cssClasses += ' table-disabled';
                    anchor.css('cursor', 'not-allowed');
                    anchor.attr('title', `Table ${tableId} is disabled${tableData.disabled_until ? ' until ' + tableData.disabled_until : ''}`);
                } else {
                    anchor.css('cursor', 'pointer');
                    anchor.removeAttr('title');
                }
                anchor.attr('class', cssClasses);
            } else {
                // Fallback for tables without data
                const table = { id: tableId, status: 'active' };
                rect.attr('fill', this.getTableColor(table));
            }
        }
    }

    /**
     * Load booking data for current date and floor
     */
    loadBookingData() {
        const currentDate = $('#myDate').val() || new Date().toISOString().split('T')[0];
        const formattedDate = this.formatDate(currentDate);

        // Load booked tables
        $.ajax({
            url: 'get_booked_tables.php',
            type: 'GET',
            data: { 
                useDate: formattedDate, 
                useZone: this.currentFloor.toString()
            },
            dataType: 'json',
            success: (data) => {
                this.bookedTables = Array.isArray(data) ? data : [];
                this.updateAllTableAppearances();
            },
            error: (xhr, status, error) => {
                console.error('Error loading booked tables:', error);
                this.bookedTables = [];
            }
        });

        // Load disabled tables
        $.ajax({
            url: 'get_disabled_tables.php',
            type: 'GET',
            data: {
                useDate: formattedDate, // Pass the selected date
                useZone: this.currentFloor.toString() // Pass the current floor
            },
            dataType: 'json',
            success: (data) => {
                // Handle both old format (array) and new format (object with tables array)
                if (data && data.status === 'success') {
                    this.disabledTables = Array.isArray(data.tables) ? data.tables : [];
                    console.log(`Loaded ${data.count} disabled tables for date ${data.selected_date}`);
                } else if (Array.isArray(data)) {
                    // Backward compatibility with old format
                    this.disabledTables = data;
                } else {
                    this.disabledTables = [];
                }
                this.updateAllTableAppearances();
            },
            error: (xhr, status, error) => {
                console.error('Error loading disabled tables:', error);
                this.disabledTables = [];
            }
        });
    }

    /**
     * Update appearance of all tables
     */
    updateAllTableAppearances() {
        $('.svg-box[id^="box-"]:not([id*="VIP"])').each((index, element) => {
            const tableId = $(element).attr('id').replace('box-', '');
            this.updateTableAppearance(tableId);
        });
    }

    /**
     * Format date for API calls
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    /**
     * Get currently selected tables
     */
    getSelectedTables() {
        return this.selectedTables;
    }

    /**
     * Clear all selected tables
     */
    clearSelectedTables() {
        this.selectedTables = [];
        this.updateAllTableAppearances();
    }

    /**
     * Set selected tables programmatically
     */
    setSelectedTables(tableIds) {
        this.selectedTables = Array.isArray(tableIds) ? tableIds : [];
        this.updateAllTableAppearances();
    }
}

// Initialize the dynamic table renderer when document is ready
$(document).ready(function() {
    // Check if dynamic tables should be enabled
    const enableDynamicTables = window.ENABLE_DYNAMIC_TABLES !== false;
    const replaceHardcoded = window.REPLACE_HARDCODED_TABLES === true;

    window.dynamicTableRenderer = new DynamicTableRenderer({
        enabled: enableDynamicTables,
        replaceHardcoded: replaceHardcoded
    });

    // Don't initialize immediately - wait for centralized initialization
    // window.dynamicTableRenderer.init();

    // Mark as ready for centralized initialization
    window.dynamicTableRendererReady = true;
});
