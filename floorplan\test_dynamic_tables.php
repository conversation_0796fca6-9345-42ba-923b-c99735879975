<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('location: ../login/');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Tables Test</title>
    <link href="../assets/libs/bootstrap/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .svg-box {
            cursor: pointer;
        }
        .svg-box:hover rect {
            stroke-width: 3;
        }
        .test-container {
            margin: 20px;
        }
        .floor-tabs {
            margin-bottom: 20px;
        }
        .svg-container {
            border: 1px solid #ddd;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Dynamic Tables Test Page</h1>
        <p>This page tests the dynamic table rendering system.</p>
        
        <!-- Date Selector -->
        <div class="row mb-3">
            <div class="col-md-4">
                <label for="myDate" class="form-label">Select Date:</label>
                <input type="date" id="myDate" class="form-control" value="<?php echo date('Y-m-d'); ?>">
            </div>
        </div>
        
        <!-- Floor Tabs -->
        <div class="floor-tabs">
            <ul class="nav nav-tabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <a class="nav-link active" data-bs-toggle="tab" href="#floor1" role="tab">Floor 1</a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" data-bs-toggle="tab" href="#floor2" role="tab">Floor 2</a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" data-bs-toggle="tab" href="#floor3" role="tab">Floor 3</a>
                </li>
            </ul>
        </div>
        
        <!-- Tab Content -->
        <div class="tab-content">
            <!-- Floor 1 -->
            <div class="tab-pane fade show active" id="floor1" role="tabpanel">
                <h3>Floor 1 - Dynamic Tables</h3>
                <div class="svg-container">
                    <svg width="1200" height="400" viewBox="0 0 1200 400" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background -->
                        <rect width="1200" height="400" fill="#e9ecef" stroke="#dee2e6" stroke-width="1"/>
                        <text x="600" y="30" font-size="20" fill="#6c757d" text-anchor="middle" font-weight="bold">Floor 1 - Tables will be loaded dynamically</text>
                        
                        <!-- VIP1 Room (static for reference) -->
                        <a href="javascript:;" id="box-VIP1" class="svg-box" data-value="0">
                            <rect id="rbox-VIP1" x="50" y="50" width="110" height="150" fill="#12deb9" stroke="#ffffff" stroke-width="2" />
                            <text x="105" y="135" font-size="24" fill="#ffffff" text-anchor="middle" font-weight="bold">VIP1</text>
                        </a>
                    </svg>
                </div>
            </div>
            
            <!-- Floor 2 -->
            <div class="tab-pane fade" id="floor2" role="tabpanel">
                <h3>Floor 2 - Dynamic Tables</h3>
                <div class="svg-container">
                    <svg width="1200" height="400" viewBox="0 0 1200 400" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background -->
                        <rect width="1200" height="400" fill="#e9ecef" stroke="#dee2e6" stroke-width="1"/>
                        <text x="600" y="30" font-size="20" fill="#6c757d" text-anchor="middle" font-weight="bold">Floor 2 - Tables will be loaded dynamically</text>
                        
                        <!-- VIP2 Room (static for reference) -->
                        <a href="javascript:;" id="box-VIP2" class="svg-box" data-value="0">
                            <rect id="rbox-VIP2" x="50" y="50" width="110" height="150" fill="#12deb9" stroke="#ffffff" stroke-width="2" />
                            <text x="105" y="135" font-size="24" fill="#ffffff" text-anchor="middle" font-weight="bold">VIP2</text>
                        </a>
                    </svg>
                </div>
            </div>
            
            <!-- Floor 3 -->
            <div class="tab-pane fade" id="floor3" role="tabpanel">
                <h3>Floor 3 - Dynamic Tables</h3>
                <div class="svg-container">
                    <svg width="1200" height="400" viewBox="0 0 1200 400" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background -->
                        <rect width="1200" height="400" fill="#e9ecef" stroke="#dee2e6" stroke-width="1"/>
                        <text x="600" y="30" font-size="20" fill="#6c757d" text-anchor="middle" font-weight="bold">Floor 3 - Tables will be loaded dynamically</text>
                    </svg>
                </div>
            </div>
        </div>
        
        <!-- Debug Info -->
        <div class="mt-4">
            <h4>Debug Information</h4>
            <div class="row">
                <div class="col-md-6">
                    <h5>Selected Tables:</h5>
                    <div id="selected-tables" class="border p-2 bg-light">
                        <em>No tables selected</em>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>Actions:</h5>
                    <button id="clear-selection" class="btn btn-warning btn-sm">Clear Selection</button>
                    <button id="reload-tables" class="btn btn-info btn-sm">Reload Tables</button>
                </div>
            </div>
        </div>
        
        <!-- Links -->
        <div class="mt-4">
            <a href="populate_tables_from_svg.php" class="btn btn-primary">Populate Database</a>
            <a href="mainpage.php" class="btn btn-secondary">Back to Main Floorplan</a>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../assets/libs/jquery/dist/jquery.min.js"></script>
    <script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Configure and enable dynamic tables for this test page -->
    <script src="js/dynamic-tables-config.js"></script>
    <script>
        // Override config for test page
        window.DYNAMIC_TABLES_CONFIG.enabled = true;
        window.DYNAMIC_TABLES_CONFIG.replaceHardcoded = true;
        window.DYNAMIC_TABLES_CONFIG.debug = true;

        // Update global flags
        window.ENABLE_DYNAMIC_TABLES = true;
        window.REPLACE_HARDCODED_TABLES = true;
    </script>
    <script src="js/dynamic-tables.js"></script>
    
    <script>
        $(document).ready(function() {
            // Listen for table selection changes
            $(document).on('tableSelectionChanged', function(event, data) {
                console.log('Table selection changed:', data);
                updateSelectedTablesDisplay(data.selectedTables);
            });
            
            // Update selected tables display
            function updateSelectedTablesDisplay(selectedTables) {
                const container = $('#selected-tables');
                if (selectedTables.length === 0) {
                    container.html('<em>No tables selected</em>');
                } else {
                    container.html('<strong>Selected:</strong> ' + selectedTables.join(', '));
                }
            }
            
            // Clear selection button
            $('#clear-selection').on('click', function() {
                if (window.dynamicTableRenderer) {
                    window.dynamicTableRenderer.clearSelectedTables();
                    updateSelectedTablesDisplay([]);
                }
            });
            
            // Reload tables button
            $('#reload-tables').on('click', function() {
                if (window.dynamicTableRenderer) {
                    window.dynamicTableRenderer.loadTablesForCurrentFloor();
                }
            });
        });
    </script>
</body>
</html>
