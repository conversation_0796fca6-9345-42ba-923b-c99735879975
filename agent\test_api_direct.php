<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

echo "<h2>Direct API Test</h2>";

// Test different scenarios
$testCases = [
    '' => 'Empty search (all agents)',
    'ABC' => 'Search for ABC',
    'John' => 'Search for John',
    'test' => 'Search for test'
];

foreach ($testCases as $searchTerm => $description) {
    echo "<h3>{$description}</h3>";
    
    $url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/agent_api.php";
    if (!empty($searchTerm)) {
        $url .= "?search=" . urlencode($searchTerm);
    }
    
    echo "<p><strong>URL:</strong> <code>{$url}</code></p>";
    
    // Create context for the request
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'Cookie: ' . $_SERVER['HTTP_COOKIE']
            ]
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response === false) {
        echo "<p style='color: red;'>❌ Failed to get response</p>";
    } else {
        echo "<p style='color: green;'>✅ Response received</p>";
        
        $data = json_decode($response, true);
        if ($data === null) {
            echo "<p style='color: red;'>❌ Invalid JSON response</p>";
            echo "<h4>Raw Response:</h4>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>" . htmlspecialchars($response) . "</pre>";
        } else {
            echo "<h4>JSON Response:</h4>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>" . json_encode($data, JSON_PRETTY_PRINT) . "</pre>";
            
            if ($data['success']) {
                echo "<p style='color: green;'>✅ API call successful</p>";
                if (isset($data['data'])) {
                    echo "<p><strong>Number of results:</strong> " . count($data['data']) . "</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ API error: {$data['message']}</p>";
            }
        }
    }
    
    echo "<hr>";
}

echo "<div style='margin-top: 20px;'>";
echo "<a href='index.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>← Back to Agent Management</a>";
echo "</div>";
?>
