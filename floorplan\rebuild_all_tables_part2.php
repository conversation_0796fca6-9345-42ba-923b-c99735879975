<?php
/**
 * <PERSON><PERSON><PERSON> to add Floor 3 tables to kp_tables database
 * Part 2 of the complete rebuild process
 */

session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

include '../dbconnect/_dbconnect.php';

try {
    $conn = db_connect();
    
    echo "<h2>Adding Floor 3 Tables (Part 2)</h2>";
    
    // Floor 3 Tables
    $floor3Tables = [
        'A1', 'A1-1', 'A1-2', 'A1-3', 'A1-4', 'A1-5', 'A2', 'A2-1', 'A2-2', 'A2-3', 'A2-4', 'A2-5', 'A2-6', 
        'A3', 'A3-1', 'A3-2', 'A3-3', 'A3-4', 'A3-5', 'A3-6', 'A4', 'A4-1', 'A4-2', 'A4-3', 'A4-4', 'A4-5', 'A4-6', 
        'A5', 'A5-1', 'A5-2', 'A5-3', 'A5-4', 'A5-5', 'A5-6', 'A6', 'A6-1', 'A6-2', 'A6-3', 'A6-4', 'A6-5', 'A6-6', 
        'A7', 'A7-1', 'A7-2', 'A7-3', 'A7-4', 'A7-5', 'A7-6', 'A8', 'A8-1', 'A8-2', 'A8-3', 'A8-4', 'A8-5', 'A8-6', 
        'A9', 'A9-1', 'A9-2', 'A9-3', 'A9-4', 'A9-5', 'A9-6', 'A10', 'A10-1', 'A10-2', 'A10-3', 'A10-4', 'A10-5', 
        'A11', 'A11-1', 'A11-2', 'A11-3', 'A11-4', 'A11-5', 'A12', 'A12-1', 'A12-2', 'A12-3', 'A12-4', 'A12-5', 
        'A14', 'A14-1', 'A14-2', 'A14-3', 'A14-4', 'A14-5', 'A15', 'A15-1', 'A15-2', 'A15-3', 'A15-4', 
        'A16', 'A16-1', 'A16-2', 'A16-3', 'A16-4', 'A17', 'A17-1', 'A17-2', 'A17-3', 'A18', 'A18-1', 'A18-2', 
        'A19', 'A19-1', 'A19-2', 'A19-3', 'A20', 'A20-1', 'A20-2', 'A20-3', 'A20-4', 'A20-5', 
        'A21', 'A21-1', 'A21-2', 'A21-3', 'A21-4', 'A21-5', 'A22', 'A22-1', 'A22-2', 'A22-3', 'A22-4', 'A22-5', 
        'A23', 'A23-1', 'A23-2', 'A23-3', 'A23-4', 'A23-5', 'A24', 'A24-1', 'A24-2', 'A24-3', 'A24-4', 'A24-5', 
        'A25', 'A25-1', 'A25-2', 'A25-3', 'A25-4', 'A25-5', 'A26', 'A26-1', 'A26-2', 'A26-3', 'A26-4', 'A26-5', 
        'A27', 'A27-1', 'A27-2', 'A27-3', 'A27-4', 'A27-5', 'A28', 'A28-1', 'A28-2', 'A28-3', 'A28-4', 'A28-5', 
        'A29', 'A29-1', 'A29-2', 'A29-3', 'A29-4', 'A29-5', 'A30', 'A30-1', 'A30-2', 'A30-3', 'A30-4', 'A30-5', 
        'A31', 'A31-1', 'A31-2', 'A31-3', 'A31-4', 'A31-5', 'A32', 'A32-1', 'A32-2', 'A32-3', 'A32-4', 'A32-5', 
        'A33', 'A33-1', 'A33-2', 'A33-3', 'A33-4', 'A34', 'A34-1', 'A34-2', 'A34-3', 'A35', 'A35-1', 'A35-2', 
        'A36', 'A36-1', 'A36-2'
    ];
    
    echo "<h3>Adding Floor 3 Tables...</h3>";
    echo "<p>Total Floor 3 tables to add: " . count($floor3Tables) . "</p>";
    
    // Prepare insert statement
    $insertSql = "INSERT INTO kp_tables (table_id, x_position, y_position, width, height, status) VALUES (?, ?, ?, ?, ?, 'active')";
    $insertStmt = $conn->prepare($insertSql);
    
    // Insert Floor 3 tables
    $floor3Count = 0;
    $currentX = 100;
    $currentY = 50;
    $spacingX = 55;
    $spacingY = 35;
    $tablesPerRow = 12;
    
    foreach ($floor3Tables as $index => $tableName) {
        // Calculate position for Floor 3 tables
        $rowIndex = intval($index / $tablesPerRow);
        $colIndex = $index % $tablesPerRow;
        
        $x = $currentX + ($colIndex * $spacingX);
        $y = $currentY + ($rowIndex * $spacingY);
        
        // Main tables (without dash) are slightly larger
        if (strpos($tableName, '-') === false) {
            $width = 60;
            $height = 35;
        } else {
            $width = 50;
            $height = 30;
        }
        
        $insertStmt->execute([$tableName, $x, $y, $width, $height]);
        $floor3Count++;
        
        // Show progress every 20 tables
        if ($floor3Count % 20 == 0) {
            echo "<p style='color: blue;'>✓ Added {$floor3Count} Floor 3 tables...</p>";
        }
    }
    
    echo "<p style='color: green;'>✅ Successfully added {$floor3Count} Floor 3 tables</p>";
    
    // Get final statistics
    $totalSql = "SELECT COUNT(*) as total FROM kp_tables";
    $totalStmt = $conn->prepare($totalSql);
    $totalStmt->execute();
    $totalCount = $totalStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    $floor1Sql = "SELECT COUNT(*) as count FROM kp_tables WHERE table_id LIKE 'C%' OR table_id = 'VIP1'";
    $floor1Stmt = $conn->prepare($floor1Sql);
    $floor1Stmt->execute();
    $floor1Count = $floor1Stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $floor2Sql = "SELECT COUNT(*) as count FROM kp_tables WHERE table_id LIKE 'H%' OR table_id LIKE 'B%' OR table_id = 'VIP2' OR table_id = 'VIP3'";
    $floor2Stmt = $conn->prepare($floor2Sql);
    $floor2Stmt->execute();
    $floor2Count = $floor2Stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $floor3Sql = "SELECT COUNT(*) as count FROM kp_tables WHERE table_id LIKE 'A%'";
    $floor3Stmt = $conn->prepare($floor3Sql);
    $floor3Stmt->execute();
    $floor3CountDb = $floor3Stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "<hr>";
    echo "<h3 style='color: green;'>✅ Complete Database Rebuild Finished!</h3>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>Final Statistics:</h4>";
    echo "<ul>";
    echo "<li><strong>Floor 1:</strong> {$floor1Count} tables (VIP1 + C series)</li>";
    echo "<li><strong>Floor 2:</strong> {$floor2Count} tables (VIP2, VIP3 + H series + B series)</li>";
    echo "<li><strong>Floor 3:</strong> {$floor3CountDb} tables (A series)</li>";
    echo "<li><strong>Total Tables:</strong> {$totalCount}</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h4>Table Naming Patterns:</h4>";
    echo "<ul>";
    echo "<li><strong>Floor 1:</strong> VIP1, C1, C1-1, C1-2, C1-3, C2, C2-1, etc.</li>";
    echo "<li><strong>Floor 2:</strong> VIP2, VIP3, H1, H1-1, H1-2, B1, B1-1, etc.</li>";
    echo "<li><strong>Floor 3:</strong> A1, A1-1, A1-2, A1-3, A1-4, A1-5, etc.</li>";
    echo "</ul>";
    
    echo "<div style='margin-top: 30px;'>";
    echo "<a href='verify_all_tables.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>→ Verify All Tables</a>";
    echo "<a href='test_dynamic_tables.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>→ Test Dynamic Tables</a>";
    echo "<a href='admin_dynamic_tables.php' style='background: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>→ Admin Panel</a>";
    echo "<a href='mainpage.php' style='background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>→ Main Floorplan</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error</h3>";
    echo "<p style='color: red;'>An error occurred: " . $e->getMessage() . "</p>";
    echo "<p><a href='rebuild_all_tables.php'>← Back to Part 1</a></p>";
}
?>
