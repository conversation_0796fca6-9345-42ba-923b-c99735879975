<?php
/**
 * Get Online Users API
 * 
 * Returns list of currently online users based on recent activity
 */

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Include database connection
require_once '../dbconnect/_dbconnect.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Connect to database
    $conn = db_connect();
    
    if (!$conn) {
        throw new Exception('Database connection failed');
    }
    
    // Create user_sessions table if it doesn't exist
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS user_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            session_id VARCHAR(255) NOT NULL,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_session_id (session_id),
            INDEX idx_last_activity (last_activity),
            FOREIGN KEY (user_id) REFERENCES kp_login(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ";
    
    $conn->exec($createTableSQL);
    
    // Update current user's session activity
    $currentUserId = $_SESSION['id'];
    $sessionId = session_id();
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // Insert or update current user's session
    $updateSessionSQL = "
        INSERT INTO user_sessions (user_id, session_id, ip_address, user_agent, last_activity) 
        VALUES (?, ?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE 
        last_activity = NOW(),
        ip_address = VALUES(ip_address),
        user_agent = VALUES(user_agent)
    ";
    
    $stmt = $conn->prepare($updateSessionSQL);
    $stmt->execute([$currentUserId, $sessionId, $ipAddress, $userAgent]);
    
    // Clean up old sessions (older than 1 hour)
    $cleanupSQL = "DELETE FROM user_sessions WHERE last_activity < DATE_SUB(NOW(), INTERVAL 1 HOUR)";
    $conn->exec($cleanupSQL);
    
    // Get online users (active within last 15 minutes)
    $onlineUsersSQL = "
        SELECT DISTINCT
            l.id,
            l.name,
            l.user as username,
            l.role,
            UNIX_TIMESTAMP(MAX(us.last_activity)) as last_activity
        FROM kp_login l
        INNER JOIN user_sessions us ON l.id = us.user_id
        WHERE us.last_activity >= DATE_SUB(NOW(), INTERVAL 15 MINUTE)
        AND l.status = 1
        GROUP BY l.id, l.name, l.user, l.role
        ORDER BY MAX(us.last_activity) DESC
    ";
    
    $stmt = $conn->prepare($onlineUsersSQL);
    $stmt->execute();
    $onlineUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Return the online users
    echo json_encode([
        'success' => true,
        'users' => $onlineUsers,
        'count' => count($onlineUsers)
    ]);
    
} catch (Exception $e) {
    // Log error
    error_log("Error in get_online_users.php: " . $e->getMessage());
    
    // Return error response
    echo json_encode([
        'success' => false,
        'message' => 'Error retrieving online users: ' . $e->getMessage()
    ]);
} finally {
    // Close database connection
    if (isset($conn)) {
        db_close($conn);
    }
}
?>
