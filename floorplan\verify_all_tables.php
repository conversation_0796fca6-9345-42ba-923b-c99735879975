<?php
/**
 * <PERSON><PERSON><PERSON> to verify all tables in kp_tables database
 * Checks Floor 1, Floor 2, and Floor 3 tables
 */

session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

include '../dbconnect/_dbconnect.php';

try {
    $conn = db_connect();
    
    echo "<h2>Verifying All Tables in Database</h2>";
    
    // Expected tables for each floor
    $expectedFloor1 = [
        'VIP1', 'C1', 'C1-1', 'C1-2', 'C1-3', 'C2', 'C2-1', 'C2-2', 'C2-3', 'C2-4', 'C2-5', 
        'C3', 'C3-1', 'C3-2', 'C3-3', 'C3-4', 'C3-5', 'C4', 'C4-1', 'C4-2', 'C4-3', 'C4-4', 'C4-5', 
        'C5', 'C5-1', 'C5-2', 'C5-3', 'C5-4', 'C6', 'C6-1', 'C6-2', 'C6-3', 'C6-4', 
        'C7', 'C7-1', 'C7-2', 'C7-3', 'C7-4', 'C8', 'C8-1', 'C8-2', 'C8-3', 'C8-4', 
        'C9', 'C9-1', 'C9-2', 'C9-3', 'C9-4', 'C9-5', 'C10', 'C10-1', 'C10-2', 'C10-3', 'C10-4', 
        'C11', 'C11-1', 'C11-2', 'C11-3', 'C11-4', 'C11-5', 'C12', 'C12-1', 'C12-2', 'C12-3', 
        'C14', 'C14-1', 'C14-2', 'C14-3', 'C14-4', 'C14-5', 'C15', 'C15-1', 'C15-2', 'C15-3', 'C15-4', 'C15-5', 
        'C16', 'C16-1', 'C16-2', 'C16-3', 'C16-4', 'C16-5', 'C17', 'C17-1', 'C17-2', 'C17-3', 'C17-4', 
        'C18', 'C18-1', 'C18-2', 'C18-3', 'C18-4', 'C19', 'C19-1', 'C19-2', 'C19-3', 'C19-4', 
        'C20', 'C20-1', 'C20-2', 'C20-3', 'C20-4', 'C21', 'C21-1', 'C21-2', 'C21-3', 'C21-4', 'C21-5', 
        'C22', 'C22-1', 'C22-2', 'C22-3', 'C22-4', 'C23', 'C23-1', 'C23-2', 'C23-3', 'C23-4', 'C23-5'
    ];
    
    $expectedFloor2 = [
        'VIP2', 'VIP3', 'H1', 'H1-1', 'H1-2', 'H2', 'H2-1', 'H2-2', 'H2-3', 'H3', 'H3-1', 'H3-2', 'H3-3', 
        'H4', 'H4-1', 'H4-2', 'H4-3', 'H5', 'H5-1', 'H5-2', 'H5-3', 'H6', 'H6-1', 'H6-2', 'H6-3', 
        'H7', 'H7-1', 'H7-2', 'H8', 'H8-1', 'H8-2', 'H8-3', 'H9', 'H9-1', 'H9-2', 'H9-3', 
        'H10', 'H10-1', 'H10-2', 'H10-3', 'H11', 'H11-1', 'H11-2', 'H11-3', 'H12', 'H12-1', 'H12-2', 'H12-3', 
        'H14', 'H15', 'H15-1', 'H16', 'H16-1', 'H16-2', 'H16-3', 'H16-4', 'H17', 'H17-1', 'H17-2', 'H17-3', 'H17-4', 'H17-5', 
        'B1', 'B1-1', 'B1-2', 'B1-3', 'B2', 'B2-1', 'B2-2', 'B2-3', 'B3', 'B3-1', 'B3-2', 'B3-3', 
        'B4', 'B4-1', 'B4-2', 'B4-3', 'B5', 'B5-1', 'B5-2', 'B5-3', 'B5-4', 'B6', 'B6-1', 'B6-2', 'B6-3', 'B6-4', 
        'B7', 'B7-1', 'B7-2', 'B7-3', 'B8', 'B8-1', 'B8-2', 'B8-3', 'B8-4', 'B9', 'B9-1', 'B9-2', 'B9-3', 
        'B10', 'B10-1', 'B10-2', 'B10-3', 'B10-4', 'B11', 'B11-1', 'B11-2', 'B11-3', 'B11-4', 'B12', 'B12-1', 'B12-2', 'B12-3', 
        'B14', 'B14-1', 'B14-2', 'B14-3', 'B15', 'B15-1', 'B15-2', 'B15-3', 'B16', 'B16-1', 'B16-2', 'B16-3', 
        'B17', 'B17-1', 'B17-2', 'B17-3', 'B17-4', 'B18', 'B18-1', 'B18-2', 'B18-3', 'B18-4', 'B19', 'B19-1', 'B19-2', 'B19-3', 
        'B20', 'B20-1', 'B20-2', 'B20-3', 'B20-4', 'B21', 'B21-1', 'B21-2', 'B21-3', 'B22', 'B22-1', 'B22-2', 'B22-3', 'B22-4', 
        'B23', 'B23-1', 'B23-2', 'B23-3', 'B23-4', 'B24', 'B24-1', 'B25', 'B26'
    ];
    
    // Get actual tables from database
    $sql = "SELECT table_id FROM kp_tables ORDER BY table_id";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    
    $actualTables = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $actualTables[] = $row['table_id'];
    }
    
    // Separate actual tables by floor
    $actualFloor1 = array_filter($actualTables, function($table) {
        return $table === 'VIP1' || strpos($table, 'C') === 0;
    });
    
    $actualFloor2 = array_filter($actualTables, function($table) {
        return $table === 'VIP2' || $table === 'VIP3' || strpos($table, 'H') === 0 || strpos($table, 'B') === 0;
    });
    
    $actualFloor3 = array_filter($actualTables, function($table) {
        return strpos($table, 'A') === 0;
    });
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; margin: 20px 0;'>";
    
    // Floor 1 Verification
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;'>";
    echo "<h3>Floor 1 Verification</h3>";
    echo "<p><strong>Expected:</strong> " . count($expectedFloor1) . " tables</p>";
    echo "<p><strong>Actual:</strong> " . count($actualFloor1) . " tables</p>";
    
    $floor1Missing = array_diff($expectedFloor1, $actualFloor1);
    $floor1Extra = array_diff($actualFloor1, $expectedFloor1);
    
    if (empty($floor1Missing) && empty($floor1Extra)) {
        echo "<div style='background: #d4edda; color: #155724; padding: 10px; border-radius: 3px;'>✅ Perfect Match!</div>";
    } else {
        if (!empty($floor1Missing)) {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border-radius: 3px; margin: 5px 0;'>";
            echo "<strong>Missing:</strong> " . implode(', ', array_slice($floor1Missing, 0, 5));
            if (count($floor1Missing) > 5) echo " (+" . (count($floor1Missing) - 5) . " more)";
            echo "</div>";
        }
        if (!empty($floor1Extra)) {
            echo "<div style='background: #fff3cd; color: #856404; padding: 10px; border-radius: 3px; margin: 5px 0;'>";
            echo "<strong>Extra:</strong> " . implode(', ', array_slice($floor1Extra, 0, 5));
            if (count($floor1Extra) > 5) echo " (+" . (count($floor1Extra) - 5) . " more)";
            echo "</div>";
        }
    }
    echo "</div>";
    
    // Floor 2 Verification
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;'>";
    echo "<h3>Floor 2 Verification</h3>";
    echo "<p><strong>Expected:</strong> " . count($expectedFloor2) . " tables</p>";
    echo "<p><strong>Actual:</strong> " . count($actualFloor2) . " tables</p>";
    
    $floor2Missing = array_diff($expectedFloor2, $actualFloor2);
    $floor2Extra = array_diff($actualFloor2, $expectedFloor2);
    
    if (empty($floor2Missing) && empty($floor2Extra)) {
        echo "<div style='background: #d4edda; color: #155724; padding: 10px; border-radius: 3px;'>✅ Perfect Match!</div>";
    } else {
        if (!empty($floor2Missing)) {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border-radius: 3px; margin: 5px 0;'>";
            echo "<strong>Missing:</strong> " . implode(', ', array_slice($floor2Missing, 0, 5));
            if (count($floor2Missing) > 5) echo " (+" . (count($floor2Missing) - 5) . " more)";
            echo "</div>";
        }
        if (!empty($floor2Extra)) {
            echo "<div style='background: #fff3cd; color: #856404; padding: 10px; border-radius: 3px; margin: 5px 0;'>";
            echo "<strong>Extra:</strong> " . implode(', ', array_slice($floor2Extra, 0, 5));
            if (count($floor2Extra) > 5) echo " (+" . (count($floor2Extra) - 5) . " more)";
            echo "</div>";
        }
    }
    echo "</div>";
    
    // Floor 3 Verification (Note: We don't have the complete expected list in this script)
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;'>";
    echo "<h3>Floor 3 Verification</h3>";
    echo "<p><strong>Actual:</strong> " . count($actualFloor3) . " tables</p>";
    echo "<p><strong>Sample tables:</strong> ";
    $sampleFloor3 = array_slice($actualFloor3, 0, 10);
    echo implode(', ', $sampleFloor3);
    if (count($actualFloor3) > 10) echo " (+" . (count($actualFloor3) - 10) . " more)";
    echo "</p>";
    echo "<div style='background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 3px;'>ℹ️ Floor 3 tables present</div>";
    echo "</div>";
    
    echo "</div>";
    
    // Overall Summary
    $totalExpected = count($expectedFloor1) + count($expectedFloor2);
    $totalActual = count($actualTables);
    
    echo "<div style='background: #e9ecef; border: 1px solid #adb5bd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Overall Summary</h3>";
    echo "<ul>";
    echo "<li><strong>Floor 1:</strong> " . count($actualFloor1) . " tables</li>";
    echo "<li><strong>Floor 2:</strong> " . count($actualFloor2) . " tables</li>";
    echo "<li><strong>Floor 3:</strong> " . count($actualFloor3) . " tables</li>";
    echo "<li><strong>Total:</strong> {$totalActual} tables</li>";
    echo "</ul>";
    echo "</div>";
    
    // Navigation
    echo "<div style='margin-top: 30px;'>";
    echo "<a href='test_dynamic_tables.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>→ Test Dynamic Tables</a>";
    echo "<a href='admin_dynamic_tables.php' style='background: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>→ Admin Panel</a>";
    echo "<a href='mainpage.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>→ Main Floorplan</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error</h3>";
    echo "<p style='color: red;'>An error occurred: " . $e->getMessage() . "</p>";
}
?>
