<?php
/**
 * Test Online Users Functionality
 * 
 * Simple test page to verify online users tracking works
 */

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Location: ../login/index.php');
    exit;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Online Users</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.iconify.design/iconify-icon/1.0.7/iconify-icon.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Online Users Test</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>Current User:</strong> <?php echo $_SESSION['name']; ?> (<?php echo $_SESSION['role']; ?>)
                        </div>
                        
                        <div class="mb-3">
                            <button id="fetch-users" class="btn btn-primary">Fetch Online Users</button>
                            <button id="update-activity" class="btn btn-success">Update My Activity</button>
                        </div>
                        
                        <div id="results" class="mt-3">
                            <div class="alert alert-info">Click "Fetch Online Users" to see who's online</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('fetch-users').addEventListener('click', function() {
            fetch('get_online_users.php')
                .then(response => response.json())
                .then(data => {
                    const resultsDiv = document.getElementById('results');
                    
                    if (data.success) {
                        let html = `<div class="alert alert-success">Found ${data.count} online users:</div>`;
                        
                        if (data.users.length > 0) {
                            html += '<div class="list-group">';
                            data.users.forEach(user => {
                                const timeAgo = getTimeAgo(user.last_activity);
                                html += `
                                    <div class="list-group-item">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">${user.name}</h6>
                                            <small>${timeAgo}</small>
                                        </div>
                                        <p class="mb-1">Role: ${user.role}</p>
                                        <small>Username: ${user.username}</small>
                                    </div>
                                `;
                            });
                            html += '</div>';
                        }
                        
                        resultsDiv.innerHTML = html;
                    } else {
                        resultsDiv.innerHTML = `<div class="alert alert-danger">Error: ${data.message}</div>`;
                    }
                })
                .catch(error => {
                    document.getElementById('results').innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
                });
        });
        
        document.getElementById('update-activity').addEventListener('click', function() {
            fetch('update_user_activity.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                const resultsDiv = document.getElementById('results');
                if (data.success) {
                    resultsDiv.innerHTML = '<div class="alert alert-success">Activity updated successfully!</div>';
                } else {
                    resultsDiv.innerHTML = `<div class="alert alert-danger">Error: ${data.message}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('results').innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
            });
        });
        
        function getTimeAgo(timestamp) {
            const now = new Date();
            const activityTime = new Date(timestamp * 1000);
            const diffInSeconds = Math.floor((now - activityTime) / 1000);
            
            if (diffInSeconds < 60) return 'Just now';
            if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' minutes ago';
            if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' hours ago';
            return Math.floor(diffInSeconds / 86400) + ' days ago';
        }
    </script>
</body>
</html>
