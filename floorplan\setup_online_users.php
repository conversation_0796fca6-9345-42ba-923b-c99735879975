<?php
/**
 * Setup Online Users Functionality
 * 
 * Creates the necessary database table for online users tracking
 */

// Start session
session_start();

// Check if user is logged in and has admin privileges
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Location: ../login/index.php');
    exit;
}

if (!in_array($_SESSION['role'], ['super_admin', 'admin'])) {
    die('Access denied. Admin privileges required.');
}

// Include database connection
require_once '../dbconnect/_dbconnect.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Online Users</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Setup Online Users Functionality</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            // Connect to database
                            $conn = db_connect();
                            
                            if (!$conn) {
                                throw new Exception('Database connection failed');
                            }
                            
                            // Read and execute SQL file
                            $sql = file_get_contents('create_user_sessions_table.sql');
                            
                            if ($sql === false) {
                                throw new Exception('Could not read SQL file');
                            }
                            
                            // Execute SQL
                            $conn->exec($sql);
                            
                            echo '<div class="alert alert-success">';
                            echo '<h6>✅ Setup Completed Successfully!</h6>';
                            echo '<p>The user_sessions table has been created and the online users functionality is now ready.</p>';
                            echo '</div>';
                            
                            echo '<div class="alert alert-info">';
                            echo '<h6>📋 What was set up:</h6>';
                            echo '<ul>';
                            echo '<li>Created <code>user_sessions</code> table for tracking active sessions</li>';
                            echo '<li>Added proper indexes for performance</li>';
                            echo '<li>Set up foreign key relationship with <code>kp_login</code> table</li>';
                            echo '<li>Configured automatic cleanup of old sessions</li>';
                            echo '</ul>';
                            echo '</div>';
                            
                            echo '<div class="alert alert-warning">';
                            echo '<h6>🔧 How it works:</h6>';
                            echo '<ul>';
                            echo '<li>Users are tracked when they log in and navigate pages</li>';
                            echo '<li>Activity is updated every 5 minutes automatically</li>';
                            echo '<li>Users are considered "online" if active within the last 15 minutes</li>';
                            echo '<li>Sessions are cleaned up after 1 hour of inactivity</li>';
                            echo '<li>The online users display updates every 30 seconds</li>';
                            echo '</ul>';
                            echo '</div>';
                            
                        } catch (Exception $e) {
                            echo '<div class="alert alert-danger">';
                            echo '<h6>❌ Setup Failed</h6>';
                            echo '<p>Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
                            echo '</div>';
                        } finally {
                            // Close database connection
                            if (isset($conn)) {
                                db_close($conn);
                            }
                        }
                        ?>
                        
                        <div class="mt-4">
                            <a href="index.php" class="btn btn-primary">Go to Floorplan</a>
                            <a href="test_online_users.php" class="btn btn-secondary">Test Online Users</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
