<?php
/**
 * Test Report API
 * 
 * Simple test page to verify report API functionality
 */

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Location: ../login/index.php');
    exit;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Report API</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.iconify.design/iconify-icon/1.0.7/iconify-icon.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-10 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Test Report API</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>Current User:</strong> <?php echo $_SESSION['name']; ?> (<?php echo $_SESSION['role']; ?>)
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <button class="btn btn-primary w-100" onclick="testDailyReport()">Test Daily Report</button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-success w-100" onclick="testMonthlyReport()">Test Monthly Report</button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-warning w-100" onclick="testQuarterlyReport()">Test Quarterly Report</button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-info w-100" onclick="testYTDReport()">Test YTD Report</button>
                            </div>
                        </div>
                        
                        <div id="results" class="mt-3">
                            <div class="alert alert-info">Click a button above to test the report API</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testDailyReport() {
            testReport('daily', {
                type: 'daily',
                startDate: '<?php echo date('Y-m-d'); ?>'
            });
        }
        
        function testMonthlyReport() {
            testReport('monthly', {
                type: 'monthly',
                month: '<?php echo date('Y-m'); ?>'
            });
        }
        
        function testQuarterlyReport() {
            testReport('quarterly', {
                type: 'quarterly',
                startDate: '<?php echo date('Y-m-d', strtotime('-3 months')); ?>'
            });
        }
        
        function testYTDReport() {
            testReport('ytd', {
                type: 'ytd',
                year: '<?php echo date('Y'); ?>'
            });
        }
        
        function testReport(reportName, data) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="alert alert-info">Testing ' + reportName + ' report...</div>';
            
            fetch('api/generate_report.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    let html = '<div class="alert alert-success">✅ ' + reportName + ' report test successful!</div>';
                    html += '<div class="card mt-3">';
                    html += '<div class="card-header"><h6>Report Data Summary</h6></div>';
                    html += '<div class="card-body">';
                    html += '<div class="row">';
                    html += '<div class="col-md-3"><strong>Total Bookings:</strong> ' + (result.data.summary.total_bookings || 0) + '</div>';
                    html += '<div class="col-md-3"><strong>Total Amount:</strong> ฿' + formatNumber(result.data.summary.total_amount || 0) + '</div>';
                    html += '<div class="col-md-3"><strong>Total Adults:</strong> ' + (result.data.summary.total_adult || 0) + '</div>';
                    html += '<div class="col-md-3"><strong>Total Children:</strong> ' + (result.data.summary.total_child || 0) + '</div>';
                    html += '</div>';
                    html += '<div class="row mt-2">';
                    html += '<div class="col-md-3"><strong>Total Infants:</strong> ' + (result.data.summary.total_infant || 0) + '</div>';
                    html += '<div class="col-md-3"><strong>Total Guides:</strong> ' + (result.data.summary.total_guide || 0) + '</div>';
                    html += '<div class="col-md-3"><strong>Total FOC:</strong> ' + (result.data.summary.total_foc || 0) + '</div>';
                    html += '<div class="col-md-3"><strong>Total T/L:</strong> ' + (result.data.summary.total_tl || 0) + '</div>';
                    html += '</div>';
                    html += '<div class="mt-3">';
                    html += '<strong>Period:</strong> ' + (result.data.period_label || result.data.period);
                    html += '</div>';
                    html += '</div>';
                    html += '</div>';
                    
                    // Show floor distribution if available
                    if (result.data.floor_distribution && result.data.floor_distribution.length > 0) {
                        html += '<div class="card mt-3">';
                        html += '<div class="card-header"><h6>Floor Distribution</h6></div>';
                        html += '<div class="card-body">';
                        html += '<div class="table-responsive">';
                        html += '<table class="table table-sm">';
                        html += '<thead><tr><th>Floor</th><th>Bookings</th><th>Amount</th></tr></thead>';
                        html += '<tbody>';
                        result.data.floor_distribution.forEach(floor => {
                            html += '<tr>';
                            html += '<td>Floor ' + floor.use_zone + '</td>';
                            html += '<td>' + floor.count + '</td>';
                            html += '<td>฿' + formatNumber(floor.amount || 0) + '</td>';
                            html += '</tr>';
                        });
                        html += '</tbody></table>';
                        html += '</div>';
                        html += '</div>';
                        html += '</div>';
                    }
                    
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = '<div class="alert alert-danger">❌ ' + reportName + ' report test failed: ' + (result.message || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                resultsDiv.innerHTML = '<div class="alert alert-danger">❌ ' + reportName + ' report test failed: ' + error.message + '</div>';
            });
        }
        
        function formatNumber(num) {
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(num);
        }
    </script>
</body>
</html>
