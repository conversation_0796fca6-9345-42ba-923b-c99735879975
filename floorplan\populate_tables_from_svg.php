<?php
/**
 * <PERSON><PERSON><PERSON> to populate kp_tables database with table positions from existing SVG
 * This script extracts table positions from the hardcoded SVG elements and inserts them into the database
 */

session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

include '../dbconnect/_dbconnect.php';

try {
    $conn = db_connect();
    
    echo "<h2>Populating kp_tables from SVG Data</h2>";
    
    // Clear existing data
    echo "<h3>Clearing existing table data...</h3>";
    $clearSql = "DELETE FROM kp_tables WHERE table_id NOT LIKE 'VIP%'";
    $clearStmt = $conn->prepare($clearSql);
    $clearStmt->execute();
    echo "<p style='color: orange;'>✓ Cleared existing non-VIP table data</p>";
    
    // Floor 1 Tables - Sample data based on the SVG structure
    echo "<h3>Inserting Floor 1 Tables...</h3>";
    $floor1Tables = [
        // Row C tables (from SVG analysis)
        ['C12', 360, 38, 64, 35],
        ['C14', 425, 38, 64, 35],
        ['C15', 490, 38, 64, 35],
        ['C16', 555, 38, 64, 35],
        ['C17', 620, 38, 64, 35],
        ['C18', 685, 38, 64, 35],
        ['C19', 750, 38, 64, 35],
        ['C20', 815, 38, 64, 35],
        ['C21', 880, 38, 64, 35],
        ['C22', 945, 38, 64, 35],
        ['C23', 1010, 38, 64, 35],
        
        // Row C/1 tables (second row)
        ['C12/1', 360, 75, 64, 35],
        ['C14/1', 425, 75, 64, 35],
        ['C15/1', 490, 75, 64, 35],
        ['C16/1', 555, 75, 64, 35],
        ['C17/1', 620, 75, 64, 35],
        ['C18/1', 685, 75, 64, 35],
        ['C19/1', 750, 75, 64, 35],
        ['C20/1', 815, 75, 64, 35],
        ['C21/1', 880, 75, 64, 35],
        ['C22/1', 945, 75, 64, 35],
        ['C23/1', 1010, 75, 64, 35],
        
        // Row C/2 tables (third row)
        ['C12/2', 360, 112, 64, 35],
        ['C14/2', 425, 112, 64, 35],
        ['C15/2', 490, 112, 64, 35],
        ['C16/2', 555, 112, 64, 35],
        ['C17/2', 620, 112, 64, 35],
        ['C18/2', 685, 112, 64, 35],
        ['C19/2', 750, 112, 64, 35],
        ['C20/2', 815, 112, 64, 35],
        ['C21/2', 880, 112, 64, 35],
        ['C22/2', 945, 112, 64, 35],
        ['C23/2', 1010, 112, 64, 35],
        
        // Add more rows as needed - A1/1 to A1/15 format
        ['A1/1', 100, 200, 50, 30],
        ['A1/2', 155, 200, 50, 30],
        ['A1/3', 210, 200, 50, 30],
        ['A1/4', 265, 200, 50, 30],
        ['A1/5', 320, 200, 50, 30],
        ['A1/6', 375, 200, 50, 30],
        ['A1/7', 430, 200, 50, 30],
        ['A1/8', 485, 200, 50, 30],
        ['A1/9', 540, 200, 50, 30],
        ['A1/10', 595, 200, 50, 30],
        ['A1/11', 650, 200, 50, 30],
        ['A1/12', 705, 200, 50, 30],
        ['A1/13', 760, 200, 50, 30],
        ['A1/14', 815, 200, 50, 30],
        ['A1/15', 870, 200, 50, 30],
        
        // A2 row
        ['A2/1', 100, 235, 50, 30],
        ['A2/2', 155, 235, 50, 30],
        ['A2/3', 210, 235, 50, 30],
        ['A2/4', 265, 235, 50, 30],
        ['A2/5', 320, 235, 50, 30],
        ['A2/6', 375, 235, 50, 30],
        ['A2/7', 430, 235, 50, 30],
        ['A2/8', 485, 235, 50, 30],
        ['A2/9', 540, 235, 50, 30],
        ['A2/10', 595, 235, 50, 30],
        ['A2/11', 650, 235, 50, 30],
        ['A2/12', 705, 235, 50, 30],
        ['A2/13', 760, 235, 50, 30],
        ['A2/14', 815, 235, 50, 30],
        ['A2/15', 870, 235, 50, 30],
    ];
    
    $insertSql = "INSERT INTO kp_tables (table_id, x_position, y_position, width, height, status) VALUES (?, ?, ?, ?, ?, 'active')";
    $insertStmt = $conn->prepare($insertSql);
    
    $floor1Count = 0;
    foreach ($floor1Tables as $table) {
        $insertStmt->execute($table);
        $floor1Count++;
    }
    echo "<p style='color: green;'>✓ Inserted {$floor1Count} Floor 1 tables</p>";
    
    // Floor 2 Tables - Sample data
    echo "<h3>Inserting Floor 2 Tables...</h3>";
    $floor2Tables = [];
    
    // Generate 2A01-2A15 tables
    for ($i = 1; $i <= 15; $i++) {
        $tableId = '2A' . str_pad($i, 2, '0', STR_PAD_LEFT);
        $x = 100 + ($i - 1) * 55;
        $y = 50;
        $floor2Tables[] = [$tableId, $x, $y, 50, 30];
    }
    
    // Generate 2B01-2B15 tables
    for ($i = 1; $i <= 15; $i++) {
        $tableId = '2B' . str_pad($i, 2, '0', STR_PAD_LEFT);
        $x = 100 + ($i - 1) * 55;
        $y = 85;
        $floor2Tables[] = [$tableId, $x, $y, 50, 30];
    }
    
    // Generate 2H01-2H15 tables (bow section)
    for ($i = 1; $i <= 15; $i++) {
        $tableId = '2H' . str_pad($i, 2, '0', STR_PAD_LEFT);
        $x = 100 + ($i - 1) * 55;
        $y = 150;
        $floor2Tables[] = [$tableId, $x, $y, 50, 30];
    }
    
    $floor2Count = 0;
    foreach ($floor2Tables as $table) {
        $insertStmt->execute($table);
        $floor2Count++;
    }
    echo "<p style='color: green;'>✓ Inserted {$floor2Count} Floor 2 tables</p>";
    
    // Floor 3 Tables - Sample data
    echo "<h3>Inserting Floor 3 Tables...</h3>";
    $floor3Tables = [];
    
    // Generate 3A01-3A15 tables
    for ($i = 1; $i <= 15; $i++) {
        $tableId = '3A' . str_pad($i, 2, '0', STR_PAD_LEFT);
        $x = 100 + ($i - 1) * 55;
        $y = 50;
        $floor3Tables[] = [$tableId, $x, $y, 50, 30];
    }
    
    // Generate 3B01-3B15 tables
    for ($i = 1; $i <= 15; $i++) {
        $tableId = '3B' . str_pad($i, 2, '0', STR_PAD_LEFT);
        $x = 100 + ($i - 1) * 55;
        $y = 85;
        $floor3Tables[] = [$tableId, $x, $y, 50, 30];
    }
    
    // Generate 3C01-3C15 tables
    for ($i = 1; $i <= 15; $i++) {
        $tableId = '3C' . str_pad($i, 2, '0', STR_PAD_LEFT);
        $x = 100 + ($i - 1) * 55;
        $y = 120;
        $floor3Tables[] = [$tableId, $x, $y, 50, 30];
    }
    
    $floor3Count = 0;
    foreach ($floor3Tables as $table) {
        $insertStmt->execute($table);
        $floor3Count++;
    }
    echo "<p style='color: green;'>✓ Inserted {$floor3Count} Floor 3 tables</p>";
    
    // Insert VIP rooms if they don't exist
    echo "<h3>Checking VIP Rooms...</h3>";
    $vipCheckSql = "SELECT COUNT(*) FROM kp_tables WHERE table_id LIKE 'VIP%'";
    $vipCheckStmt = $conn->prepare($vipCheckSql);
    $vipCheckStmt->execute();
    $vipCount = $vipCheckStmt->fetchColumn();
    
    if ($vipCount == 0) {
        $vipRooms = [
            ['VIP1', 210, 290, 110, 150],
            ['VIP2', 200, 100, 110, 150],
            ['VIP3', 350, 100, 110, 150],
        ];
        
        foreach ($vipRooms as $vip) {
            $insertStmt->execute($vip);
        }
        echo "<p style='color: green;'>✓ Inserted VIP rooms</p>";
    } else {
        echo "<p style='color: blue;'>✓ VIP rooms already exist</p>";
    }
    
    // Summary
    $totalSql = "SELECT COUNT(*) FROM kp_tables";
    $totalStmt = $conn->prepare($totalSql);
    $totalStmt->execute();
    $totalCount = $totalStmt->fetchColumn();
    
    echo "<hr>";
    echo "<h3 style='color: green;'>✅ Database Population Complete!</h3>";
    echo "<p><strong>Total tables in database: {$totalCount}</strong></p>";
    echo "<ul>";
    echo "<li>Floor 1: {$floor1Count} tables</li>";
    echo "<li>Floor 2: {$floor2Count} tables</li>";
    echo "<li>Floor 3: {$floor3Count} tables</li>";
    echo "<li>VIP Rooms: " . ($vipCount > 0 ? $vipCount : 3) . " rooms</li>";
    echo "</ul>";
    
    echo "<p><a href='mainpage.php' class='btn btn-primary'>→ Go to Floorplan</a></p>";
    echo "<p><a href='../tableStup/mainpage.php' class='btn btn-secondary'>→ Table Setup</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error</h3>";
    echo "<p style='color: red;'>An error occurred: " . $e->getMessage() . "</p>";
}
?>
