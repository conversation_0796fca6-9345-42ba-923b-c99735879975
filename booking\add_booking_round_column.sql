-- Add booking_round column to kp_booking table if it doesn't exist
-- This script is safe to run multiple times

-- Check and add booking_round column
SET @exists_booking_round = 0;
SELECT COUNT(*) INTO @exists_booking_round
FROM information_schema.columns
WHERE table_schema = DATABASE()
    AND table_name = 'kp_booking'
    AND column_name = 'booking_round';

SET @sql_booking_round = IF(
        @exists_booking_round = 0,
        'ALTER TABLE kp_booking ADD COLUMN booking_round VARCHAR(20) DEFAULT "Sunset" COMMENT "Booking round: Sunset, Dinner"',
        'SELECT "Column booking_round already exists" as message'
    );

PREPARE stmt
FROM @sql_booking_round;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
