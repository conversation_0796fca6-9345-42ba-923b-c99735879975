<?php
/**
 * Export Report to PDF
 * 
 * Exports report data to PDF format using mPDF
 */

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Location: ../../login/index.php');
    exit;
}

// Include database connection
require_once '../../dbconnect/_dbconnect.php';

// Check if mPDF is available
$mpdfPath = '../../vendor/mpdf/mpdf/src/Mpdf.php';
if (!file_exists($mpdfPath)) {
    // Fallback to simple HTML output if mPDF is not available
    generateSimplePDF();
    exit;
}

require_once $mpdfPath;

try {
    // Get parameters
    $reportType = $_GET['type'] ?? 'daily';
    $startDate = $_GET['startDate'] ?? date('Y-m-d');
    $endDate = $_GET['endDate'] ?? date('Y-m-d');
    $month = $_GET['month'] ?? date('Y-m');
    $year = $_GET['year'] ?? date('Y');
    
    // Connect to database
    $conn = db_connect();
    
    if (!$conn) {
        throw new Exception('Database connection failed');
    }
    
    // Get report data based on type
    $reportData = generateReportData($conn, $reportType, $startDate, $endDate, $month, $year);
    
    // Generate PDF
    $mpdf = new \Mpdf\Mpdf([
        'mode' => 'utf-8',
        'format' => 'A4-L', // Landscape for better table display
        'margin_left' => 10,
        'margin_right' => 10,
        'margin_top' => 10,
        'margin_bottom' => 10
    ]);
    
    $html = generatePDFContent($reportData, $reportType);
    
    $mpdf->WriteHTML($html);
    
    $filename = "Report_" . ucfirst($reportType) . "_" . date('Y-m-d') . ".pdf";
    $mpdf->Output($filename, 'I'); // 'I' for inline display, 'D' for download
    
} catch (Exception $e) {
    error_log("Error in export_report_pdf.php: " . $e->getMessage());
    echo "Error generating PDF report: " . $e->getMessage();
} finally {
    if (isset($conn)) {
        db_close($conn);
    }
}

function generateSimplePDF() {
    // Fallback HTML output when mPDF is not available
    header('Content-Type: text/html; charset=utf-8');
    echo '<h2>PDF Export</h2>';
    echo '<p>PDF library not available. Please install mPDF or use Excel export instead.</p>';
    echo '<script>window.print();</script>';
}

function generateReportData($conn, $reportType, $startDate, $endDate, $month, $year) {
    switch ($reportType) {
        case 'daily':
            return getDailyReportData($conn, $startDate);
        case 'monthly':
            return getMonthlyReportData($conn, $month);
        case 'quarterly':
            return getQuarterlyReportData($conn, $startDate);
        case 'ytd':
            return getYearToDateReportData($conn, $year);
        default:
            throw new Exception('Invalid report type');
    }
}

function getDailyReportData($conn, $date) {
    $sql = "SELECT 
                orderNo, name, phone, adult, child, infant, guide, inspection as foc, team_leader as tl,
                use_date, use_zone, voucher, agent, remark, amount, payment_status, pay_type,
                book_status, create_date
            FROM kp_booking 
            WHERE use_date = ? 
            AND book_status != 'Cancel'
            ORDER BY create_date DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$date]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getMonthlyReportData($conn, $month) {
    $year = substr($month, 0, 4);
    $monthNum = substr($month, 5, 2);
    
    $sql = "SELECT 
                orderNo, name, phone, adult, child, infant, guide, inspection as foc, team_leader as tl,
                use_date, use_zone, voucher, agent, remark, amount, payment_status, pay_type,
                book_status, create_date
            FROM kp_booking 
            WHERE YEAR(use_date) = ? AND MONTH(use_date) = ?
            AND book_status != 'Cancel'
            ORDER BY use_date DESC, create_date DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$year, $monthNum]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getQuarterlyReportData($conn, $startDate) {
    $endDate = date('Y-m-d', strtotime($startDate . ' +3 months'));
    
    $sql = "SELECT 
                orderNo, name, phone, adult, child, infant, guide, inspection as foc, team_leader as tl,
                use_date, use_zone, voucher, agent, remark, amount, payment_status, pay_type,
                book_status, create_date
            FROM kp_booking 
            WHERE use_date >= ? AND use_date < ?
            AND book_status != 'Cancel'
            ORDER BY use_date DESC, create_date DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$startDate, $endDate]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getYearToDateReportData($conn, $year) {
    $sql = "SELECT 
                orderNo, name, phone, adult, child, infant, guide, inspection as foc, team_leader as tl,
                use_date, use_zone, voucher, agent, remark, amount, payment_status, pay_type,
                book_status, create_date
            FROM kp_booking 
            WHERE YEAR(use_date) = ?
            AND book_status != 'Cancel'
            ORDER BY use_date DESC, create_date DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$year]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function generatePDFContent($data, $reportType) {
    $html = '<html>';
    $html .= '<head>';
    $html .= '<meta charset="UTF-8">';
    $html .= '<style>';
    $html .= 'body { font-family: Arial, sans-serif; font-size: 10px; }';
    $html .= 'h1, h2 { color: #333; text-align: center; }';
    $html .= 'table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }';
    $html .= 'th, td { border: 1px solid #ddd; padding: 4px; text-align: left; font-size: 8px; }';
    $html .= 'th { background-color: #f2f2f2; font-weight: bold; text-align: center; }';
    $html .= '.summary-table { width: 50%; margin: 20px auto; }';
    $html .= '.summary-table th, .summary-table td { font-size: 10px; padding: 6px; }';
    $html .= '.text-center { text-align: center; }';
    $html .= '.text-right { text-align: right; }';
    $html .= '</style>';
    $html .= '</head>';
    $html .= '<body>';
    
    $html .= '<h1>Sawasdee Backend - ' . ucfirst($reportType) . ' Report</h1>';
    $html .= '<p class="text-center">Generated on: ' . date('Y-m-d H:i:s') . '</p>';
    
    if (empty($data)) {
        $html .= '<p class="text-center">No data found for the selected period.</p>';
    } else {
        // Summary first
        $totalBookings = count($data);
        $totalAmount = array_sum(array_column($data, 'amount'));
        $totalAdult = array_sum(array_column($data, 'adult'));
        $totalChild = array_sum(array_column($data, 'child'));
        $totalInfant = array_sum(array_column($data, 'infant'));
        $totalGuide = array_sum(array_column($data, 'guide'));
        $totalFoc = array_sum(array_column($data, 'foc'));
        $totalTl = array_sum(array_column($data, 'tl'));
        
        $html .= '<h2>Summary</h2>';
        $html .= '<table class="summary-table">';
        $html .= '<tr><th>Metric</th><th>Value</th></tr>';
        $html .= '<tr><td><strong>Total Bookings</strong></td><td class="text-right">' . $totalBookings . '</td></tr>';
        $html .= '<tr><td><strong>Total Amount</strong></td><td class="text-right">฿' . number_format($totalAmount, 2) . '</td></tr>';
        $html .= '<tr><td><strong>Total Adults</strong></td><td class="text-right">' . $totalAdult . '</td></tr>';
        $html .= '<tr><td><strong>Total Children</strong></td><td class="text-right">' . $totalChild . '</td></tr>';
        $html .= '<tr><td><strong>Total Infants</strong></td><td class="text-right">' . $totalInfant . '</td></tr>';
        $html .= '<tr><td><strong>Total Guests</strong></td><td class="text-right">' . ($totalAdult + $totalChild + $totalInfant) . '</td></tr>';
        $html .= '<tr><td><strong>Total Guides</strong></td><td class="text-right">' . $totalGuide . '</td></tr>';
        $html .= '<tr><td><strong>Total FOC</strong></td><td class="text-right">' . $totalFoc . '</td></tr>';
        $html .= '<tr><td><strong>Total T/L</strong></td><td class="text-right">' . $totalTl . '</td></tr>';
        $html .= '</table>';
        
        // Detailed data table
        $html .= '<h2>Detailed Bookings</h2>';
        $html .= '<table>';
        $html .= '<thead>';
        $html .= '<tr>';
        $html .= '<th>Order No</th>';
        $html .= '<th>Customer</th>';
        $html .= '<th>Phone</th>';
        $html .= '<th>Use Date</th>';
        $html .= '<th>Floor</th>';
        $html .= '<th>Adult</th>';
        $html .= '<th>Child</th>';
        $html .= '<th>Infant</th>';
        $html .= '<th>Guide</th>';
        $html .= '<th>FOC</th>';
        $html .= '<th>T/L</th>';
        $html .= '<th>Amount</th>';
        $html .= '<th>Payment</th>';
        $html .= '<th>Status</th>';
        $html .= '</tr>';
        $html .= '</thead>';
        $html .= '<tbody>';
        
        foreach ($data as $row) {
            $html .= '<tr>';
            $html .= '<td>' . htmlspecialchars($row['orderNo'] ?? '') . '</td>';
            $html .= '<td>' . htmlspecialchars(substr($row['name'] ?? '', 0, 15)) . '</td>';
            $html .= '<td>' . htmlspecialchars($row['phone'] ?? '') . '</td>';
            $html .= '<td>' . htmlspecialchars($row['use_date'] ?? '') . '</td>';
            $html .= '<td class="text-center">' . htmlspecialchars($row['use_zone'] ?? '') . '</td>';
            $html .= '<td class="text-center">' . htmlspecialchars($row['adult'] ?? '0') . '</td>';
            $html .= '<td class="text-center">' . htmlspecialchars($row['child'] ?? '0') . '</td>';
            $html .= '<td class="text-center">' . htmlspecialchars($row['infant'] ?? '0') . '</td>';
            $html .= '<td class="text-center">' . htmlspecialchars($row['guide'] ?? '0') . '</td>';
            $html .= '<td class="text-center">' . htmlspecialchars($row['foc'] ?? '0') . '</td>';
            $html .= '<td class="text-center">' . htmlspecialchars($row['tl'] ?? '0') . '</td>';
            $html .= '<td class="text-right">฿' . number_format($row['amount'] ?? 0, 2) . '</td>';
            $html .= '<td class="text-center">' . htmlspecialchars($row['payment_status'] ?? '') . '</td>';
            $html .= '<td class="text-center">' . htmlspecialchars($row['book_status'] ?? '') . '</td>';
            $html .= '</tr>';
        }
        
        $html .= '</tbody>';
        $html .= '</table>';
    }
    
    $html .= '</body>';
    $html .= '</html>';
    
    return $html;
}
?>
