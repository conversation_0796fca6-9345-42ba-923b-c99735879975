<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>Agent API Debug</h2>";

try {
    // Include database connection
    echo "<p>1. Including database connection...</p>";
    include '../dbconnect/_dbconnect.php';
    echo "<p>✓ Database connection file included</p>";

    // Set headers for JSON response
    header('Content-Type: application/json');

    // Start session if not already started
    echo "<p>2. Starting session...</p>";
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    echo "<p>✓ Session started</p>";

    // Check if user is logged in
    echo "<p>3. Checking login status...</p>";
    if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
        echo "<p>❌ User not logged in</p>";
        echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
        exit;
    }
    echo "<p>✓ User is logged in</p>";

    // Get the request method
    echo "<p>4. Getting request method...</p>";
    $method = $_SERVER['REQUEST_METHOD'];
    echo "<p>✓ Request method: {$method}</p>";

    // Connect to database
    echo "<p>5. Connecting to database...</p>";
    $conn = db_connect();
    echo "<p>✓ Database connected</p>";

    // Test if kp_agent table exists
    echo "<p>6. Checking if kp_agent table exists...</p>";
    $sql = "SHOW TABLES LIKE 'kp_agent'";
    $result = $conn->query($sql);
    $tableExists = $result->rowCount() > 0;
    
    if (!$tableExists) {
        echo "<p>❌ kp_agent table does not exist</p>";
        echo json_encode(['success' => false, 'message' => 'Agent table does not exist']);
        exit;
    }
    echo "<p>✓ kp_agent table exists</p>";

    // Handle GET request (default case)
    if ($method === 'GET') {
        echo "<p>7. Handling GET request...</p>";
        
        // Get search parameter
        $search = isset($_GET['search']) ? $_GET['search'] : '';
        echo "<p>Search parameter: '{$search}'</p>";
        
        if (!empty($search)) {
            // Search for agents
            echo "<p>8. Searching for agents...</p>";
            $sql = "SELECT * FROM kp_agent WHERE name LIKE :search OR contact_person LIKE :search OR phone LIKE :search OR email LIKE :search ORDER BY name ASC";
            $stmt = $conn->prepare($sql);
            $searchParam = "%{$search}%";
            $stmt->bindParam(':search', $searchParam, PDO::PARAM_STR);
        } else {
            // Get all agents
            echo "<p>8. Getting all agents...</p>";
            $sql = "SELECT * FROM kp_agent ORDER BY name ASC";
            $stmt = $conn->prepare($sql);
        }
        
        echo "<p>SQL Query: {$sql}</p>";
        
        $stmt->execute();
        $agents = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>✓ Query executed successfully</p>";
        echo "<p>Number of agents found: " . count($agents) . "</p>";
        
        // Show first few agents for debugging
        if (count($agents) > 0) {
            echo "<p>Sample agent data:</p>";
            echo "<pre>" . print_r($agents[0], true) . "</pre>";
        }
        
        echo json_encode(['success' => true, 'data' => $agents]);
    } else {
        echo "<p>Method {$method} not handled in debug mode</p>";
        echo json_encode(['success' => false, 'message' => 'Method not handled in debug mode']);
    }

    // Close database connection
    echo "<p>9. Closing database connection...</p>";
    db_close($conn);
    echo "<p>✓ Database connection closed</p>";

} catch (Exception $e) {
    echo "<p>❌ Exception caught: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo json_encode(['success' => false, 'message' => 'Exception: ' . $e->getMessage()]);
} catch (Error $e) {
    echo "<p>❌ Fatal error caught: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo json_encode(['success' => false, 'message' => 'Fatal error: ' . $e->getMessage()]);
}
?>
