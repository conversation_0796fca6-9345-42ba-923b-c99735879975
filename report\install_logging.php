<?php
/**
 * Install Booking Logging System
 * 
 * This script creates the necessary database table for booking edit logging
 */

session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('location:../login');
    exit;
}

// Check user permissions (only super_admin can install)
require_once('../includes/role_check.php');
if (!isSuperAdmin()) {
    die('Access denied. Only super administrators can install the logging system.');
}

require_once('../dbconnect/_dbconnect.php');

try {
    $pdo = db_connect();
    
    // Read and execute the SQL file
    $sqlFile = __DIR__ . '/create_booking_edit_log_table.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception('SQL file not found: ' . $sqlFile);
    }
    
    $sql = file_get_contents($sqlFile);
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $pdo->beginTransaction();
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^--/', $statement)) {
            $pdo->exec($statement);
        }
    }
    
    $pdo->commit();
    
    echo "<h2>✅ Booking Logging System Installed Successfully!</h2>";
    echo "<p>The booking edit log table has been created successfully.</p>";
    echo "<p><a href='edit_logs.php'>View Edit Logs</a></p>";
    echo "<p><a href='index.php'>Back to Booking System</a></p>";
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollback();
    }
    
    echo "<h2>❌ Installation Failed</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><a href='index.php'>Back to Booking System</a></p>";
}
?>
