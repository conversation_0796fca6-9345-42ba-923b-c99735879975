<?php
/**
 * <PERSON><PERSON><PERSON> to verify Floor 1 tables in kp_tables database
 * Checks that all expected table names are present
 */

session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

include '../dbconnect/_dbconnect.php';

try {
    $conn = db_connect();
    
    echo "<h2>Verifying Floor 1 Tables in Database</h2>";
    
    // Expected table names
    $expectedTables = [
        'C1', 'C1-1', 'C1-2', 'C1-3', 
        'C2', 'C2-1', 'C2-2', 'C2-3', 'C2-4', 'C2-5', 
        'C3', 'C3-1', 'C3-2', 'C3-3', 'C3-4', 'C3-5', 
        'C4', 'C4-1', 'C4-2', 'C4-3', 'C4-4', 'C4-5', 
        'C5', 'C5-1', 'C5-2', 'C5-3', 'C5-4', 
        'C6', 'C6-1', 'C6-2', 'C6-3', 'C6-4', 
        'C7', 'C7-1', 'C7-2', 'C7-3', 'C7-4', 
        'C8', 'C8-1', 'C8-2', 'C8-3', 'C8-4', 
        'C9', 'C9-1', 'C9-2', 'C9-3', 'C9-4', 'C9-5', 
        'C10', 'C10-1', 'C10-2', 'C10-3', 'C10-4', 
        'C11', 'C11-1', 'C11-2', 'C11-3', 'C11-4', 'C11-5', 
        'C12', 'C12-1', 'C12-2', 'C12-3', 
        'C14', 'C14-1', 'C14-2', 'C14-3', 'C14-4', 'C14-5', 
        'C15', 'C15-1', 'C15-2', 'C15-3', 'C15-4', 'C15-5', 
        'C16', 'C16-1', 'C16-2', 'C16-3', 'C16-4', 'C16-5', 
        'C17', 'C17-1', 'C17-2', 'C17-3', 'C17-4', 
        'C18', 'C18-1', 'C18-2', 'C18-3', 'C18-4', 
        'C19', 'C19-1', 'C19-2', 'C19-3', 'C19-4', 
        'C20', 'C20-1', 'C20-2', 'C20-3', 'C20-4', 
        'C21', 'C21-1', 'C21-2', 'C21-3', 'C21-4', 'C21-5', 
        'C22', 'C22-1', 'C22-2', 'C22-3', 'C22-4', 
        'C23', 'C23-1', 'C23-2', 'C23-3', 'C23-4', 'C23-5'
    ];
    
    echo "<p><strong>Expected tables:</strong> " . count($expectedTables) . "</p>";
    
    // Get actual tables from database
    $sql = "SELECT table_id FROM kp_tables WHERE table_id LIKE 'C%' ORDER BY 
            CAST(SUBSTRING_INDEX(table_id, '-', 1) AS UNSIGNED), 
            CAST(SUBSTRING_INDEX(table_id, '-', -1) AS UNSIGNED)";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    
    $actualTables = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $actualTables[] = $row['table_id'];
    }
    
    echo "<p><strong>Actual tables in database:</strong> " . count($actualTables) . "</p>";
    
    // Compare expected vs actual
    $missing = array_diff($expectedTables, $actualTables);
    $extra = array_diff($actualTables, $expectedTables);
    
    echo "<h3>Verification Results:</h3>";
    
    if (empty($missing) && empty($extra)) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✅ Perfect Match!</h4>";
        echo "<p>All expected tables are present in the database and no extra tables found.</p>";
        echo "</div>";
    } else {
        if (!empty($missing)) {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>❌ Missing Tables (" . count($missing) . "):</h4>";
            echo "<p>" . implode(', ', $missing) . "</p>";
            echo "</div>";
        }
        
        if (!empty($extra)) {
            echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>⚠️ Extra Tables (" . count($extra) . "):</h4>";
            echo "<p>" . implode(', ', $extra) . "</p>";
            echo "</div>";
        }
    }
    
    // Show table breakdown by main table groups
    echo "<h3>Table Breakdown by Groups:</h3>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 20px 0;'>";
    
    $groups = [];
    foreach ($actualTables as $table) {
        $mainTable = strpos($table, '-') !== false ? explode('-', $table)[0] : $table;
        if (!isset($groups[$mainTable])) {
            $groups[$mainTable] = [];
        }
        $groups[$mainTable][] = $table;
    }
    
    ksort($groups, SORT_NATURAL);
    
    foreach ($groups as $mainTable => $subTables) {
        echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; border-radius: 5px;'>";
        echo "<h5 style='margin: 0 0 10px 0; color: #495057;'>{$mainTable} Group (" . count($subTables) . ")</h5>";
        echo "<small style='color: #6c757d;'>" . implode(', ', $subTables) . "</small>";
        echo "</div>";
    }
    
    echo "</div>";
    
    // API Test
    echo "<h3>API Test:</h3>";
    echo "<p>Testing get_tables.php API for Floor 1...</p>";
    
    $apiUrl = "get_tables.php?floor=1&status=all";
    $apiData = file_get_contents("http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/" . $apiUrl);
    $apiTables = json_decode($apiData, true);
    
    if ($apiTables && !isset($apiTables['error'])) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✅ API Test Successful</h4>";
        echo "<p>API returned " . count($apiTables) . " tables for Floor 1</p>";
        echo "<p><strong>Sample tables:</strong> ";
        $sampleTables = array_slice($apiTables, 0, 5);
        foreach ($sampleTables as $table) {
            echo $table['id'] . " ";
        }
        if (count($apiTables) > 5) {
            echo "... (and " . (count($apiTables) - 5) . " more)";
        }
        echo "</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>❌ API Test Failed</h4>";
        echo "<p>Error: " . (isset($apiTables['error']) ? $apiTables['error'] : 'Unknown error') . "</p>";
        echo "</div>";
    }
    
    // Navigation links
    echo "<div style='margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px;'>";
    echo "<h4>Next Steps:</h4>";
    echo "<div style='margin-top: 15px;'>";
    echo "<a href='test_dynamic_tables.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>→ Test Dynamic Tables</a>";
    echo "<a href='mainpage.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>→ Main Floorplan</a>";
    echo "<a href='admin_dynamic_tables.php' style='background: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>→ Admin Panel</a>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error</h3>";
    echo "<p style='color: red;'>An error occurred: " . $e->getMessage() . "</p>";
}
?>
