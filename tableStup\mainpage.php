<div id="main-wrapper">
    <!-- Sidebar Start -->
    <aside class="side-mini-panel with-vertical">
        <!-- ---------------------------------- -->
        <!-- Start Vertical Layout Sidebar -->
        <!-- ---------------------------------- -->
        <div class="iconbar">
            <div>

                <?php
                    include_once("../_menuMain.php");
                    include_once("_menuPages.php");
                ?>

            </div>
        </div>
    </aside>
    <!--  Sidebar End -->


    <div class="page-wrapper">
        <?php
            include_once("_menuTop.php");
        ?>

        <div class="body-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Table Setup</h4>
                                <p class="card-subtitle">Manage tables on all floors</p>
                            </div>
                            <div class="card-body">
                                <!-- Floor Selection -->
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="floor-selector" class="form-label fw-bold">
                                                <iconify-icon icon="solar:buildings-linear" class="fs-5 me-1"></iconify-icon>
                                                Select Floor
                                            </label>
                                            <select id="floor-selector" class="form-select">
                                                <option value="1">Floor 1</option>
                                                <option value="2">Floor 2</option>
                                                <option value="3">Floor 3</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="d-flex align-items-end h-100">
                                            <div class="floor-info">
                                                <div id="floor-info-display" class="alert alert-info mb-0">
                                                    <iconify-icon icon="solar:info-circle-linear" class="fs-5 me-2"></iconify-icon>
                                                    <span id="floor-description">Floor 1: Main dining area with VIP section and C-rows (C1-C23), total 118 tables</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <ul class="nav nav-tabs theme-tab gap-3 flex-nowrap" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link active" data-bs-toggle="tab" href="#table-status" role="tab" aria-selected="true">
                                            <div class="hstack gap-2">
                                                <iconify-icon icon="solar:widget-linear" class="fs-4"></iconify-icon>
                                                <span>Table Status</span>
                                            </div>
                                        </a>
                                    </li>
                                    <!-- <li class="nav-item" role="presentation">
                                        <a class="nav-link" data-bs-toggle="tab" href="#add-tables" role="tab" aria-selected="false">
                                            <div class="hstack gap-2">
                                                <iconify-icon icon="solar:add-circle-linear" class="fs-4"></iconify-icon>
                                                <span>Add Tables</span>
                                            </div>
                                        </a>
                                    </li> -->
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link" data-bs-toggle="tab" href="#bulk-operations" role="tab" aria-selected="false">
                                            <div class="hstack gap-2">
                                                <iconify-icon icon="solar:settings-linear" class="fs-4"></iconify-icon>
                                                <span>Bulk Operations</span>
                                            </div>
                                        </a>
                                    </li>
                                </ul>

                                <div class="tab-content">
                                    <!-- Table Status Tab -->
                                    <div class="tab-pane fade show active" id="table-status" role="tabpanel">
                                        <div class="p-3">
                                            <div class="row mb-4">
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="table-row-filter" class="form-label">Filter by Row</label>
                                                        <select id="table-row-filter" class="form-select">
                                                            <option value="all">All Rows</option>
                                                            <!-- Options will be populated based on selected floor -->
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="table-status-filter" class="form-label">Filter by Status</label>
                                                        <select id="table-status-filter" class="form-select">
                                                            <option value="all">All Statuses</option>
                                                            <option value="active">Active</option>
                                                            <option value="disabled">Disabled</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label class="form-label">Quick Actions</label>
                                                        <div class="d-flex gap-2">
                                                            <button id="enable-all-floor" class="btn btn-sm btn-success">
                                                                <iconify-icon icon="solar:check-circle-linear" class="fs-5 me-1"></iconify-icon>
                                                                Enable All
                                                            </button>
                                                            <button id="disable-all-floor" class="btn btn-sm btn-warning">
                                                                <iconify-icon icon="solar:close-circle-linear" class="fs-5 me-1"></iconify-icon>
                                                                Disable All
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="table-responsive">
                                                <table id="table-list" class="table table-striped table-bordered">
                                                    <thead>
                                                        <tr>
                                                            <th>Table ID</th>
                                                            <th>Row</th>
                                                            <th>Status</th>
                                                            <th>Date Info</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <!-- Table data will be loaded here via AJAX -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Add Tables Tab -->
                                    <div class="tab-pane fade" id="add-tables" role="tabpanel">
                                        <div class="p-3">
                                            <form id="add-table-form">
                                                <div class="row mb-3">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label for="table-row" class="form-label">Row</label>
                                                            <select id="table-row" name="table-row" class="form-select" required>
                                                                <option value="">Select Row</option>
                                                                <!-- Options will be populated based on selected floor -->
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label for="table-start" class="form-label">Start Number</label>
                                                            <input type="number" id="table-start" name="table-start" class="form-control" min="1" max="99" required>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label for="table-end" class="form-label">End Number</label>
                                                            <input type="number" id="table-end" name="table-end" class="form-control" min="1" max="99" required>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row mb-3">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label for="table-x-start" class="form-label">X Position Start</label>
                                                            <input type="number" id="table-x-start" name="table-x-start" class="form-control" required>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label for="table-y-position" class="form-label">Y Position</label>
                                                            <input type="number" id="table-y-position" name="table-y-position" class="form-control" required>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row mb-3">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label for="table-width" class="form-label">Table Width</label>
                                                            <input type="number" id="table-width" name="table-width" class="form-control" value="54" required>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label for="table-height" class="form-label">Table Height</label>
                                                            <input type="number" id="table-height" name="table-height" class="form-control" value="36" required>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row mb-3">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label for="table-spacing" class="form-label">Horizontal Spacing</label>
                                                            <input type="number" id="table-spacing" name="table-spacing" class="form-control" value="57" required>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label for="table-status" class="form-label">Initial Status</label>
                                                            <select id="table-status" name="table-status" class="form-select" required>
                                                                <option value="active">Active</option>
                                                                <option value="disabled">Disabled</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-12">
                                                        <button type="submit" class="btn btn-primary">Add Tables</button>
                                                        <button type="button" id="preview-tables" class="btn btn-secondary">Preview</button>
                                                    </div>
                                                </div>
                                            </form>

                                            <div id="table-preview" class="mt-4" style="display: none;">
                                                <h5>Preview</h5>
                                                <div class="border p-3">
                                                    <svg id="preview-svg" width="1200" height="200" viewBox="0 0 1600 200">
                                                        <!-- Preview will be rendered here -->
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Bulk Operations Tab -->
                                    <div class="tab-pane fade" id="bulk-operations" role="tabpanel">
                                        <div class="p-3">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="card border-primary">
                                                        <div class="card-header bg-primary text-white">
                                                            <h5 class="card-title mb-0">
                                                                <iconify-icon icon="solar:check-circle-linear" class="fs-5 me-2"></iconify-icon>
                                                                Enable Tables
                                                            </h5>
                                                        </div>
                                                        <div class="card-body">
                                                            <p class="card-text">Enable all tables on the selected floor or specific rows.</p>
                                                            <div class="form-group mb-3">
                                                                <label for="enable-row-select" class="form-label">Select Rows to Enable</label>
                                                                <select id="enable-row-select" class="form-select" multiple>
                                                                    <option value="all">All Rows</option>
                                                                    <!-- Options will be populated based on selected floor -->
                                                                </select>
                                                                <small class="text-muted">Hold Ctrl/Cmd to select multiple rows</small>
                                                            </div>
                                                            <button id="bulk-enable-btn" class="btn btn-success w-100">
                                                                <iconify-icon icon="solar:check-circle-linear" class="fs-5 me-2"></iconify-icon>
                                                                Enable Selected Tables
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="card border-warning">
                                                        <div class="card-header bg-warning text-dark">
                                                            <h5 class="card-title mb-0">
                                                                <iconify-icon icon="solar:close-circle-linear" class="fs-5 me-2"></iconify-icon>
                                                                Disable Tables
                                                            </h5>
                                                        </div>
                                                        <div class="card-body">
                                                            <p class="card-text">Disable all tables on the selected floor or specific rows.</p>
                                                            <div class="form-group mb-3">
                                                                <label for="disable-row-select" class="form-label">Select Rows to Disable</label>
                                                                <select id="disable-row-select" class="form-select" multiple>
                                                                    <option value="all">All Rows</option>
                                                                    <!-- Options will be populated based on selected floor -->
                                                                </select>
                                                                <small class="text-muted">Hold Ctrl/Cmd to select multiple rows</small>
                                                            </div>
                                                            <div class="form-group mb-3">
                                                                <label for="bulk-disable-date" class="form-label">Disable Until Date</label>
                                                                <input type="date" id="bulk-disable-date" class="form-control">
                                                                <div class="form-check mt-2">
                                                                    <input class="form-check-input" type="checkbox" id="bulk-disable-indefinitely">
                                                                    <label class="form-check-label" for="bulk-disable-indefinitely">
                                                                        Disable indefinitely
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <button id="bulk-disable-btn" class="btn btn-warning w-100">
                                                                <iconify-icon icon="solar:close-circle-linear" class="fs-5 me-2"></iconify-icon>
                                                                Disable Selected Tables
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="offcanvas customizer offcanvas-end" tabindex="-1" id="offcanvasExample"
            aria-labelledby="offcanvasExampleLabel">
            <div class="d-flex align-items-center justify-content-between p-3 border-bottom">
                <h4 class="offcanvas-title fw-semibold" id="offcanvasExampleLabel">
                    Settings
                </h4>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body" data-simplebar style="height: calc(100vh - 80px)">
                <h6 class="fw-semibold fs-4 mb-2">Theme</h6>

                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <input type="radio" class="btn-check light-layout" name="theme-layout" id="light-layout"
                        autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="light-layout">
                        <i class="icon ti ti-brightness-up fs-7 me-2"></i>Light
                    </label>

                    <input type="radio" class="btn-check dark-layout" name="theme-layout" id="dark-layout"
                        autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="dark-layout">
                        <i class="icon ti ti-moon fs-7 me-2"></i>Dark
                    </label>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Theme Direction</h6>
                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <input type="radio" class="btn-check" name="direction-l" id="ltr-layout" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="ltr-layout">
                        <i class="icon ti ti-text-direction-ltr fs-7 me-2"></i>LTR
                    </label>

                    <input type="radio" class="btn-check" name="direction-l" id="rtl-layout" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="rtl-layout">
                        <i class="icon ti ti-text-direction-rtl fs-7 me-2"></i>RTL
                    </label>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Theme Colors</h6>

                <div class="d-flex flex-row flex-wrap gap-3 customizer-box color-pallete" role="group">
                    <input type="radio" class="btn-check" name="color-theme-layout" id="Blue_Theme"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Blue_Theme')" for="Blue_Theme" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="BLUE_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-1">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="Aqua_Theme"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Aqua_Theme')" for="Aqua_Theme" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="AQUA_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-2">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="Purple_Theme"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Purple_Theme')" for="Purple_Theme" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="PURPLE_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-3">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="green-theme-layout"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Green_Theme')" for="green-theme-layout" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="GREEN_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-4">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="cyan-theme-layout"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Cyan_Theme')" for="cyan-theme-layout" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="CYAN_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-5">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="orange-theme-layout"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Orange_Theme')" for="orange-theme-layout" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="ORANGE_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-6">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Layout Type</h6>
                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <div>
                        <input type="radio" class="btn-check" name="page-layout" id="vertical-layout"
                            autocomplete="off" />
                        <label class="btn p-9 btn-outline-primary rounded-2" for="vertical-layout">
                            <i class="icon ti ti-layout-sidebar-right fs-7 me-2"></i>Vertical
                        </label>
                    </div>
                    <div>
                        <input type="radio" class="btn-check" name="page-layout" id="horizontal-layout"
                            autocomplete="off" />
                        <label class="btn p-9 btn-outline-primary rounded-2" for="horizontal-layout">
                            <i class="icon ti ti-layout-navbar fs-7 me-2"></i>Horizontal
                        </label>
                    </div>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Container Option</h6>

                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <input type="radio" class="btn-check" name="layout" id="boxed-layout" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="boxed-layout">
                        <i class="icon ti ti-layout-distribute-vertical fs-7 me-2"></i>Boxed
                    </label>

                    <input type="radio" class="btn-check" name="layout" id="full-layout" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="full-layout">
                        <i class="icon ti ti-layout-distribute-horizontal fs-7 me-2"></i>Full
                    </label>
                </div>

                <h6 class="fw-semibold fs-4 mb-2 mt-5">Sidebar Type</h6>
                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <a href="javascript:void(0)" class="fullsidebar">
                        <input type="radio" class="btn-check" name="sidebar-type" id="full-sidebar"
                            autocomplete="off" />
                        <label class="btn p-9 btn-outline-primary rounded-2" for="full-sidebar">
                            <i class="icon ti ti-layout-sidebar-right fs-7 me-2"></i>Full
                        </label>
                    </a>
                    <div>
                        <input type="radio" class="btn-check" name="sidebar-type" id="mini-sidebar"
                            autocomplete="off" />
                        <label class="btn p-9 btn-outline-primary rounded-2" for="mini-sidebar">
                            <i class="icon ti ti-layout-sidebar fs-7 me-2"></i>Collapse
                        </label>
                    </div>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Card With</h6>

                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <input type="radio" class="btn-check" name="card-layout" id="card-with-border" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="card-with-border">
                        <i class="icon ti ti-border-outer fs-7 me-2"></i>Border
                    </label>

                    <input type="radio" class="btn-check" name="card-layout" id="card-without-border"
                        autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="card-without-border">
                        <i class="icon ti ti-border-none fs-7 me-2"></i>Shadow
                    </label>
                </div>
            </div>
        </div>

        <script>
            function handleColorTheme(e) {
                document.documentElement.setAttribute("data-color-theme", e);
            }

        </script>
    </div>
</div>
