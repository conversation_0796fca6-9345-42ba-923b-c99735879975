<?php
/**
 * Test script for get_disabled_tables.php with date parameter
 */

session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

echo "<h2>Testing get_disabled_tables.php with Date Parameter</h2>";

// Test dates
$today = date('Y-m-d');
$tomorrow = date('Y-m-d', strtotime('+1 day'));
$yesterday = date('Y-m-d', strtotime('-1 day'));

$testDates = [
    'Today' => $today,
    'Tomorrow' => $tomorrow,
    'Yesterday' => $yesterday
];

echo "<p>This script tests the get_disabled_tables.php API with different date parameters.</p>";

echo "<h3>Test Results:</h3>";

foreach ($testDates as $label => $date) {
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>{$label} ({$date})</h4>";
    
    // Make API call
    $apiUrl = "get_disabled_tables.php?date=" . urlencode($date);
    $fullUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/" . $apiUrl;
    
    echo "<p><strong>API URL:</strong> <code>{$apiUrl}</code></p>";
    
    $response = @file_get_contents($fullUrl);
    
    if ($response === false) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border-radius: 3px;'>";
        echo "❌ Failed to fetch data from API";
        echo "</div>";
    } else {
        $data = json_decode($response, true);
        
        if ($data === null) {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border-radius: 3px;'>";
            echo "❌ Invalid JSON response";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
            echo "</div>";
        } else {
            echo "<div style='background: #d4edda; color: #155724; padding: 10px; border-radius: 3px;'>";
            echo "✅ API Response Successful";
            echo "</div>";
            
            if (isset($data['status']) && $data['status'] === 'success') {
                echo "<p><strong>Selected Date:</strong> {$data['selected_date']}</p>";
                echo "<p><strong>Disabled Tables Count:</strong> {$data['count']}</p>";
                
                if ($data['count'] > 0) {
                    echo "<p><strong>Disabled Tables:</strong></p>";
                    echo "<ul>";
                    foreach ($data['tables'] as $table) {
                        $disabledUntil = $table['disabled_until'] ? $table['disabled_until'] : 'Permanently';
                        echo "<li><strong>{$table['id']}</strong> - Status: {$table['status']}, Disabled Until: {$disabledUntil}</li>";
                    }
                    echo "</ul>";
                } else {
                    echo "<p><em>No disabled tables found for this date.</em></p>";
                }
            } else {
                // Handle old format or error
                echo "<p><strong>Raw Response:</strong></p>";
                echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;'>";
                echo htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT));
                echo "</pre>";
            }
        }
    }
    
    echo "</div>";
}

// Direct database query for comparison
echo "<h3>Direct Database Query (for comparison):</h3>";

include '../dbconnect/_dbconnect.php';

try {
    $conn = db_connect();
    
    $sql = "SELECT table_id, status, disabled_until FROM kp_tables WHERE status = 'disabled' ORDER BY table_id";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    
    $allDisabled = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($allDisabled) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Table ID</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Disabled Until</th>";
        echo "<th style='padding: 8px;'>Should be disabled today?</th>";
        echo "</tr>";
        
        foreach ($allDisabled as $table) {
            $shouldBeDisabled = '';
            $rowColor = '';
            
            if ($table['disabled_until'] === null) {
                $shouldBeDisabled = 'Yes (permanent)';
                $rowColor = 'background: #f8d7da;';
            } elseif ($table['disabled_until'] >= $today) {
                $shouldBeDisabled = 'Yes (until ' . $table['disabled_until'] . ')';
                $rowColor = 'background: #f8d7da;';
            } else {
                $shouldBeDisabled = 'No (expired)';
                $rowColor = 'background: #fff3cd;';
            }
            
            echo "<tr style='{$rowColor}'>";
            echo "<td style='padding: 8px;'><strong>" . htmlspecialchars($table['table_id']) . "</strong></td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($table['status']) . "</td>";
            echo "<td style='padding: 8px;'>" . ($table['disabled_until'] ? htmlspecialchars($table['disabled_until']) : '<em>NULL</em>') . "</td>";
            echo "<td style='padding: 8px;'>" . $shouldBeDisabled . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p><em>No disabled tables found in database.</em></p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border-radius: 3px;'>";
    echo "Database Error: " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "<h3>Test Instructions:</h3>";
echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<ol>";
echo "<li>First run <a href='test_disabled_tables.php'>test_disabled_tables.php</a> to set up test data</li>";
echo "<li>Check the API responses above for different dates</li>";
echo "<li>Verify that the API correctly filters disabled tables based on the date parameter</li>";
echo "<li>Test the main floorplan page to see if date changes affect disabled table display</li>";
echo "</ol>";
echo "</div>";

echo "<div style='margin-top: 30px;'>";
echo "<a href='test_disabled_tables.php' style='background: #ffc107; color: #212529; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>→ Set Up Test Data</a>";
echo "<a href='test_dynamic_tables.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>→ Test Dynamic Tables</a>";
echo "<a href='mainpage.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>→ Main Floorplan</a>";
echo "</div>";
?>
