<?php
/**
 * <PERSON><PERSON><PERSON> to insert all tables for all floors into the database
 * This script will populate the kp_tables database with all Floor 1, 2, and 3 tables
 */

session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

include '../dbconnect/_dbconnect.php';

try {
    // Connect to database
    $conn = db_connect();
    
    // Define all tables for all floors
    
    // Floor 1 Tables (118 tables)
    $floor1Tables = [
        'VIP1', 'VIP2',
        'C1', 'C1-1', 'C1-2', 'C1-3',
        'C2', 'C2-1', 'C2-2', 'C2-3', 'C2-4', 'C2-5',
        'C3', 'C3-1', 'C3-2', 'C3-3', 'C3-4', 'C3-5',
        'C4', 'C4-1', 'C4-2', 'C4-3', 'C4-4', 'C4-5',
        'C5', 'C5-1', 'C5-2', 'C5-3', 'C5-4',
        'C6', 'C6-1', 'C6-2', 'C6-3', 'C6-4',
        'C7', 'C7-1', 'C7-2', 'C7-3', 'C7-4',
        'C8', 'C8-1', 'C8-2', 'C8-3', 'C8-4',
        'C9', 'C9-1', 'C9-2', 'C9-3', 'C9-4', 'C9-5',
        'C10', 'C10-1', 'C10-2', 'C10-3', 'C10-4',
        'C11', 'C11-1', 'C11-2', 'C11-3', 'C11-4', 'C11-5',
        'C12', 'C12-1', 'C12-2', 'C12-3',
        'C14', 'C14-1', 'C14-2', 'C14-3', 'C14-4', 'C14-5',
        'C15', 'C15-1', 'C15-2', 'C15-3', 'C15-4', 'C15-5',
        'C16', 'C16-1', 'C16-2', 'C16-3', 'C16-4', 'C16-5',
        'C17', 'C17-1', 'C17-2', 'C17-3', 'C17-4',
        'C18', 'C18-1', 'C18-2', 'C18-3', 'C18-4',
        'C19', 'C19-1', 'C19-2', 'C19-3', 'C19-4',
        'C20', 'C20-1', 'C20-2', 'C20-3', 'C20-4',
        'C21', 'C21-1', 'C21-2', 'C21-3', 'C21-4', 'C21-5',
        'C22', 'C22-1', 'C22-2', 'C22-3', 'C22-4',
        'C23', 'C23-1', 'C23-2', 'C23-3', 'C23-4', 'C23-5'
    ];
    
    // Floor 2 Tables (160 tables)
    $floor2Tables = [
        'H1', 'H1-1', 'H1-2',
        'H2', 'H2-1', 'H2-2', 'H2-3',
        'H3', 'H3-1', 'H3-2', 'H3-3',
        'H4', 'H4-1', 'H4-2', 'H4-3',
        'H5', 'H5-1', 'H5-2', 'H5-3',
        'H6', 'H6-1', 'H6-2', 'H6-3',
        'H7', 'H7-1', 'H7-2',
        'H8', 'H8-1', 'H8-2', 'H8-3',
        'H9', 'H9-1', 'H9-2', 'H9-3',
        'H10', 'H10-1', 'H10-2', 'H10-3',
        'H11', 'H11-1', 'H11-2', 'H11-3',
        'H12', 'H12-1', 'H12-2', 'H12-3',
        'H14', 'H15', 'H15-1',
        'H16', 'H16-1', 'H16-2', 'H16-3', 'H16-4',
        'H17', 'H17-1', 'H17-2', 'H17-3', 'H17-4', 'H17-5',
        'B1', 'B1-1', 'B1-2', 'B1-3',
        'B2', 'B2-1', 'B2-2', 'B2-3',
        'B3', 'B3-1', 'B3-2', 'B3-3',
        'B4', 'B4-1', 'B4-2', 'B4-3',
        'B5', 'B5-1', 'B5-2', 'B5-3', 'B5-4',
        'B6', 'B6-1', 'B6-2', 'B6-3', 'B6-4',
        'B7', 'B7-1', 'B7-2', 'B7-3',
        'B8', 'B8-1', 'B8-2', 'B8-3', 'B8-4',
        'B9', 'B9-1', 'B9-2', 'B9-3',
        'B10', 'B10-1', 'B10-2', 'B10-3', 'B10-4',
        'B11', 'B11-1', 'B11-2', 'B11-3', 'B11-4',
        'B12', 'B12-1', 'B12-2', 'B12-3',
        'B14', 'B14-1', 'B14-2', 'B14-3',
        'B15', 'B15-1', 'B15-2', 'B15-3',
        'B16', 'B16-1', 'B16-2', 'B16-3',
        'B17', 'B17-1', 'B17-2', 'B17-3', 'B17-4',
        'B18', 'B18-1', 'B18-2', 'B18-3', 'B18-4',
        'B19', 'B19-1', 'B19-2', 'B19-3',
        'B20', 'B20-1', 'B20-2', 'B20-3', 'B20-4',
        'B21', 'B21-1', 'B21-2', 'B21-3',
        'B22', 'B22-1', 'B22-2', 'B22-3', 'B22-4',
        'B23', 'B23-1', 'B23-2', 'B23-3', 'B23-4',
        'B24', 'B24-1', 'B25', 'B26'
    ];
    
    // Floor 3 Tables (200 tables)
    $floor3Tables = [
        'A1', 'A1-1', 'A1-2', 'A1-3', 'A1-4', 'A1-5',
        'A2', 'A2-1', 'A2-2', 'A2-3', 'A2-4', 'A2-5', 'A2-6',
        'A3', 'A3-1', 'A3-2', 'A3-3', 'A3-4', 'A3-5', 'A3-6',
        'A4', 'A4-1', 'A4-2', 'A4-3', 'A4-4', 'A4-5', 'A4-6',
        'A5', 'A5-1', 'A5-2', 'A5-3', 'A5-4', 'A5-5', 'A5-6',
        'A6', 'A6-1', 'A6-2', 'A6-3', 'A6-4', 'A6-5', 'A6-6',
        'A7', 'A7-1', 'A7-2', 'A7-3', 'A7-4', 'A7-5', 'A7-6',
        'A8', 'A8-1', 'A8-2', 'A8-3', 'A8-4', 'A8-5', 'A8-6',
        'A9', 'A9-1', 'A9-2', 'A9-3', 'A9-4', 'A9-5', 'A9-6',
        'A10', 'A10-1', 'A10-2', 'A10-3', 'A10-4', 'A10-5',
        'A11', 'A11-1', 'A11-2', 'A11-3', 'A11-4', 'A11-5',
        'A12', 'A12-1', 'A12-2', 'A12-3', 'A12-4', 'A12-5',
        'A14', 'A14-1', 'A14-2', 'A14-3', 'A14-4', 'A14-5',
        'A15', 'A15-1', 'A15-2', 'A15-3', 'A15-4',
        'A16', 'A16-1', 'A16-2', 'A16-3', 'A16-4',
        'A17', 'A17-1', 'A17-2', 'A17-3',
        'A18', 'A18-1', 'A18-2',
        'A19', 'A19-1', 'A19-2', 'A19-3',
        'A20', 'A20-1', 'A20-2', 'A20-3', 'A20-4', 'A20-5',
        'A21', 'A21-1', 'A21-2', 'A21-3', 'A21-4', 'A21-5',
        'A22', 'A22-1', 'A22-2', 'A22-3', 'A22-4', 'A22-5',
        'A23', 'A23-1', 'A23-2', 'A23-3', 'A23-4', 'A23-5',
        'A24', 'A24-1', 'A24-2', 'A24-3', 'A24-4', 'A24-5',
        'A25', 'A25-1', 'A25-2', 'A25-3', 'A25-4', 'A25-5',
        'A26', 'A26-1', 'A26-2', 'A26-3', 'A26-4', 'A26-5',
        'A27', 'A27-1', 'A27-2', 'A27-3', 'A27-4', 'A27-5',
        'A28', 'A28-1', 'A28-2', 'A28-3', 'A28-4', 'A28-5',
        'A29', 'A29-1', 'A29-2', 'A29-3', 'A29-4', 'A29-5',
        'A30', 'A30-1', 'A30-2', 'A30-3', 'A30-4', 'A30-5',
        'A31', 'A31-1', 'A31-2', 'A31-3', 'A31-4', 'A31-5',
        'A32', 'A32-1', 'A32-2', 'A32-3', 'A32-4', 'A32-5',
        'A33', 'A33-1', 'A33-2', 'A33-3', 'A33-4',
        'A34', 'A34-1', 'A34-2', 'A34-3',
        'A35', 'A35-1', 'A35-2',
        'A36', 'A36-1', 'A36-2'
    ];

    // Combine all tables
    $allTables = array_merge($floor1Tables, $floor2Tables, $floor3Tables);

    echo "<h2>Inserting All Tables for All Floors</h2>";
    echo "<p><strong>Floor 1:</strong> " . count($floor1Tables) . " tables</p>";
    echo "<p><strong>Floor 2:</strong> " . count($floor2Tables) . " tables</p>";
    echo "<p><strong>Floor 3:</strong> " . count($floor3Tables) . " tables</p>";
    echo "<p><strong>Total:</strong> " . count($allTables) . " tables</p>";
    echo "<hr>";

    // Clear existing tables first (optional - uncomment if you want to start fresh)
    // echo "<h3>Clearing existing tables...</h3>";
    // $clearSql = "DELETE FROM kp_tables";
    // $clearStmt = $conn->prepare($clearSql);
    // $clearStmt->execute();
    // echo "<p>Cleared existing tables.</p>";

    // Insert all tables
    echo "<h3>Inserting tables...</h3>";
    $insertSql = "INSERT IGNORE INTO kp_tables (table_id, floor, status, x_position, y_position, width, height, disabled_until) VALUES (?, ?, 'active', 0, 0, 50, 30, NULL)";
    $insertStmt = $conn->prepare($insertSql);

    $addedCount = 0;
    $skippedCount = 0;
    $errorCount = 0;

    // Insert Floor 1 tables
    echo "<h4>Floor 1 Tables:</h4>";
    foreach ($floor1Tables as $tableId) {
        try {
            $insertStmt->execute([$tableId, 1]);
            if ($insertStmt->rowCount() > 0) {
                $addedCount++;
                echo "<span style='color: green;'>✓</span> $tableId ";
            } else {
                $skippedCount++;
                echo "<span style='color: orange;'>⚠</span> $tableId ";
            }
        } catch (Exception $e) {
            $errorCount++;
            echo "<span style='color: red;'>✗</span> $tableId ";
        }
    }
    echo "<br><br>";

    // Insert Floor 2 tables
    echo "<h4>Floor 2 Tables:</h4>";
    foreach ($floor2Tables as $tableId) {
        try {
            $insertStmt->execute([$tableId, 2]);
            if ($insertStmt->rowCount() > 0) {
                $addedCount++;
                echo "<span style='color: green;'>✓</span> $tableId ";
            } else {
                $skippedCount++;
                echo "<span style='color: orange;'>⚠</span> $tableId ";
            }
        } catch (Exception $e) {
            $errorCount++;
            echo "<span style='color: red;'>✗</span> $tableId ";
        }
    }
    echo "<br><br>";

    // Insert Floor 3 tables
    echo "<h4>Floor 3 Tables:</h4>";
    foreach ($floor3Tables as $tableId) {
        try {
            $insertStmt->execute([$tableId, 3]);
            if ($insertStmt->rowCount() > 0) {
                $addedCount++;
                echo "<span style='color: green;'>✓</span> $tableId ";
            } else {
                $skippedCount++;
                echo "<span style='color: orange;'>⚠</span> $tableId ";
            }
        } catch (Exception $e) {
            $errorCount++;
            echo "<span style='color: red;'>✗</span> $tableId ";
        }
    }
    echo "<br><br>";

    echo "<h3>Summary</h3>";
    echo "<ul>";
    echo "<li><strong>Tables added:</strong> <span style='color: green;'>$addedCount</span></li>";
    echo "<li><strong>Tables skipped (already existed):</strong> <span style='color: orange;'>$skippedCount</span></li>";
    echo "<li><strong>Errors:</strong> <span style='color: red;'>$errorCount</span></li>";
    echo "</ul>";

    // Verify the insertion
    echo "<h3>Verification</h3>";
    $verifySql = "SELECT COUNT(*) as count FROM kp_tables";
    $verifyStmt = $conn->prepare($verifySql);
    $verifyStmt->execute();
    $verifyResult = $verifyStmt->fetch(PDO::FETCH_ASSOC);

    echo "<p><strong>Total tables in database:</strong> " . $verifyResult['count'] . "</p>";

    // Count by floor
    $floor1CountSql = "SELECT COUNT(*) as count FROM kp_tables WHERE table_id IN ('" . implode("','", $floor1Tables) . "')";
    $floor1CountStmt = $conn->prepare($floor1CountSql);
    $floor1CountStmt->execute();
    $floor1Count = $floor1CountStmt->fetch(PDO::FETCH_ASSOC);

    $floor2CountSql = "SELECT COUNT(*) as count FROM kp_tables WHERE table_id IN ('" . implode("','", $floor2Tables) . "')";
    $floor2CountStmt = $conn->prepare($floor2CountSql);
    $floor2CountStmt->execute();
    $floor2Count = $floor2CountStmt->fetch(PDO::FETCH_ASSOC);

    $floor3CountSql = "SELECT COUNT(*) as count FROM kp_tables WHERE table_id IN ('" . implode("','", $floor3Tables) . "')";
    $floor3CountStmt = $conn->prepare($floor3CountSql);
    $floor3CountStmt->execute();
    $floor3Count = $floor3CountStmt->fetch(PDO::FETCH_ASSOC);

    echo "<ul>";
    echo "<li><strong>Floor 1 tables in database:</strong> " . $floor1Count['count'] . " / " . count($floor1Tables) . "</li>";
    echo "<li><strong>Floor 2 tables in database:</strong> " . $floor2Count['count'] . " / " . count($floor2Tables) . "</li>";
    echo "<li><strong>Floor 3 tables in database:</strong> " . $floor3Count['count'] . " / " . count($floor3Tables) . "</li>";
    echo "</ul>";

    $totalExpected = count($allTables);
    $totalActual = $floor1Count['count'] + $floor2Count['count'] + $floor3Count['count'];

    if ($totalActual >= $totalExpected) {
        echo "<p style='color: green; font-weight: bold; font-size: 18px;'>✅ All tables have been successfully inserted!</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ Some tables may be missing. Expected: $totalExpected, Found: $totalActual</p>";
    }

    echo "<hr>";
    echo "<p><a href='index.php'>← Back to Table Setup</a></p>";

} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error</h3>";
    echo "<p>An error occurred: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.php'>← Back to Table Setup</a></p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Insert All Tables - All Floors</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        h2, h3, h4 {
            color: #333;
        }
        h2 {
            background-color: #007bff;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        h3 {
            background-color: #6c757d;
            color: white;
            padding: 10px;
            border-radius: 3px;
            margin-top: 20px;
        }
        h4 {
            background-color: #28a745;
            color: white;
            padding: 8px;
            border-radius: 3px;
            margin-top: 15px;
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        ul {
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        p {
            background-color: white;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        a {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        a:hover {
            background-color: #0056b3;
        }
        hr {
            border: none;
            height: 2px;
            background-color: #dee2e6;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- Content is generated above -->
</body>
</html>
