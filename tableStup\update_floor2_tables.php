<?php
/**
 * <PERSON><PERSON><PERSON> to update Floor 2 tables in the database
 * This script will add the new Floor 2 table configuration
 */

session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

include '../dbconnect/_dbconnect.php';

try {
    // Connect to database
    $conn = db_connect();
    
    // Define the new Floor 2 tables
    $floor2Tables = [
        'H1', 'H1-1', 'H1-2',
        'H2', 'H2-1', 'H2-2', 'H2-3',
        'H3', 'H3-1', 'H3-2', 'H3-3',
        'H4', 'H4-1', 'H4-2', 'H4-3',
        'H5', 'H5-1', 'H5-2', 'H5-3',
        'H6', 'H6-1', 'H6-2', 'H6-3',
        'H7', 'H7-1', 'H7-2',
        'H8', 'H8-1', 'H8-2', 'H8-3',
        'H9', 'H9-1', 'H9-2', 'H9-3',
        'H10', 'H10-1', 'H10-2', 'H10-3',
        'H11', 'H11-1', 'H11-2', 'H11-3',
        'H12', 'H12-1', 'H12-2', 'H12-3',
        'H14', 'H15', 'H15-1',
        'H16', 'H16-1', 'H16-2', 'H16-3', 'H16-4',
        'H17', 'H17-1', 'H17-2', 'H17-3', 'H17-4', 'H17-5',
        'B1', 'B1-1', 'B1-2', 'B1-3',
        'B2', 'B2-1', 'B2-2', 'B2-3',
        'B3', 'B3-1', 'B3-2', 'B3-3',
        'B4', 'B4-1', 'B4-2', 'B4-3',
        'B5', 'B5-1', 'B5-2', 'B5-3', 'B5-4',
        'B6', 'B6-1', 'B6-2', 'B6-3', 'B6-4',
        'B7', 'B7-1', 'B7-2', 'B7-3',
        'B8', 'B8-1', 'B8-2', 'B8-3', 'B8-4',
        'B9', 'B9-1', 'B9-2', 'B9-3',
        'B10', 'B10-1', 'B10-2', 'B10-3', 'B10-4',
        'B11', 'B11-1', 'B11-2', 'B11-3', 'B11-4',
        'B12', 'B12-1', 'B12-2', 'B12-3',
        'B14', 'B14-1', 'B14-2', 'B14-3',
        'B15', 'B15-1', 'B15-2', 'B15-3',
        'B16', 'B16-1', 'B16-2', 'B16-3',
        'B17', 'B17-1', 'B17-2', 'B17-3', 'B17-4',
        'B18', 'B18-1', 'B18-2', 'B18-3', 'B18-4',
        'B19', 'B19-1', 'B19-2', 'B19-3',
        'B20', 'B20-1', 'B20-2', 'B20-3', 'B20-4',
        'B21', 'B21-1', 'B21-2', 'B21-3',
        'B22', 'B22-1', 'B22-2', 'B22-3', 'B22-4',
        'B23', 'B23-1', 'B23-2', 'B23-3', 'B23-4',
        'B24', 'B24-1', 'B25', 'B26'
    ];
    
    echo "<h2>Updating Floor 2 Tables</h2>";
    echo "<p>Total tables to process: " . count($floor2Tables) . "</p>";
    
    // First, remove old Floor 2 tables (if any old format exists)
    echo "<h3>Removing old Floor 2 tables...</h3>";
    $deleteOldSql = "DELETE FROM kp_tables WHERE 
        (table_id LIKE 'B1/%' OR table_id LIKE 'B2/%' OR table_id LIKE 'B3/%' OR table_id LIKE 'B4/%' OR table_id LIKE 'B5/%' OR 
         table_id LIKE 'B6/%' OR table_id LIKE 'B7/%' OR table_id LIKE 'B8/%' OR table_id LIKE 'B9/%' OR table_id LIKE 'B10/%' OR 
         table_id LIKE 'H1/%' OR table_id LIKE 'H2/%' OR table_id LIKE 'H3/%' OR table_id LIKE 'H4/%' OR table_id LIKE 'H5/%' OR table_id LIKE 'H6/%')
        AND table_id NOT IN ('" . implode("','", $floor2Tables) . "')";
    $deleteStmt = $conn->prepare($deleteOldSql);
    $deleteStmt->execute();
    $deletedCount = $deleteStmt->rowCount();
    echo "<p>Removed $deletedCount old Floor 2 tables.</p>";
    
    // Insert new Floor 2 tables
    echo "<h3>Adding new Floor 2 tables...</h3>";
    $insertSql = "INSERT IGNORE INTO kp_tables (table_id, status, x_position, y_position, width, height, disabled_until) VALUES (?, 'active', 0, 0, 50, 30, NULL)";
    $insertStmt = $conn->prepare($insertSql);
    
    $addedCount = 0;
    $skippedCount = 0;
    
    foreach ($floor2Tables as $index => $tableId) {
        try {
            $insertStmt->execute([$tableId]);
            if ($insertStmt->rowCount() > 0) {
                $addedCount++;
                echo "<span style='color: green;'>✓</span> Added table: $tableId<br>";
            } else {
                $skippedCount++;
                echo "<span style='color: orange;'>⚠</span> Skipped (already exists): $tableId<br>";
            }
        } catch (Exception $e) {
            echo "<span style='color: red;'>✗</span> Error adding table $tableId: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<h3>Summary</h3>";
    echo "<ul>";
    echo "<li>Tables added: <strong>$addedCount</strong></li>";
    echo "<li>Tables skipped (already existed): <strong>$skippedCount</strong></li>";
    echo "<li>Old tables removed: <strong>$deletedCount</strong></li>";
    echo "</ul>";
    
    // Verify the update
    echo "<h3>Verification</h3>";
    $verifySql = "SELECT COUNT(*) as count FROM kp_tables WHERE table_id IN ('" . implode("','", $floor2Tables) . "')";
    $verifyStmt = $conn->prepare($verifySql);
    $verifyStmt->execute();
    $verifyResult = $verifyStmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p>Current Floor 2 tables in database: <strong>" . $verifyResult['count'] . "</strong></p>";
    
    if ($verifyResult['count'] == count($floor2Tables)) {
        echo "<p style='color: green; font-weight: bold;'>✅ All Floor 2 tables have been successfully updated!</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ Some tables may be missing. Please check the database.</p>";
    }
    
    echo "<hr>";
    echo "<p><a href='index.php'>← Back to Table Setup</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error</h3>";
    echo "<p>An error occurred: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.php'>← Back to Table Setup</a></p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Floor 2 Tables Update</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h2, h3 { color: #333; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <!-- Content is generated above -->
</body>
</html>
