# Dynamic Table System for Floorplan

This document explains the new dynamic table rendering system that fetches table positions from the `kp_tables` database and displays them as SVG elements.

## Overview

The dynamic table system replaces hardcoded SVG table elements with dynamically generated ones based on database data. This allows for:

- Easy table management through the database
- Consistent table positioning across floors
- Ability to add/remove/modify tables without editing HTML
- Better integration with the table setup system

## Files Added/Modified

### New Files:
1. `js/dynamic-tables.js` - Main JavaScript class for dynamic table rendering
2. `js/dynamic-tables-config.js` - Configuration file for the dynamic system
3. `populate_tables_from_svg.php` - <PERSON><PERSON><PERSON> to populate database with initial table data
4. `update_floor1_tables.php` - <PERSON><PERSON><PERSON> to update Floor 1 with new naming scheme
5. `verify_floor1_tables.php` - <PERSON><PERSON><PERSON> to verify Floor 1 table data
6. `rebuild_all_tables.php` - Complete database rebuild script (Part 1)
7. `rebuild_all_tables_part2.php` - Complete database rebuild script (Part 2)
8. `verify_all_tables.php` - Comprehensive verification for all floors
9. `table_structure_summary.php` - Complete table structure overview
10. `test_disabled_tables.php` - <PERSON><PERSON><PERSON> to test disabled table functionality
11. `reset_test_tables.php` - <PERSON><PERSON><PERSON> to reset test tables to normal status
12. `test_get_disabled_tables.php` - Script to test the get_disabled_tables.php API with date parameter
6. `test_dynamic_tables.php` - Test page for the dynamic system
7. `admin_dynamic_tables.php` - Administration panel for the dynamic system
8. `README_DYNAMIC_TABLES.md` - This documentation file

### Modified Files:
1. `get_tables.php` - Enhanced to support floor filtering and better data structure
2. `mainpage.php` - Added dynamic-tables.js script inclusion

## Database Structure

The system uses the `kp_tables` table with the following structure:

```sql
CREATE TABLE `kp_tables` (
  `table_id` VARCHAR(10) NOT NULL,
  `x_position` INT NOT NULL,
  `y_position` INT NOT NULL,
  `width` INT NOT NULL,
  `height` INT NOT NULL,
  `status` ENUM('active', 'disabled') NOT NULL DEFAULT 'active',
  `disabled_until` DATE DEFAULT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`table_id`)
);
```

## Table ID Formats

The system supports different table ID formats for different floors:

### Floor 1:
- Format: `C1`, `C1-1`, `C1-2`, `C1-3`, etc.
- Pattern: Main tables (C1, C2, C3...) and sub-tables (C1-1, C1-2...)
- Total: 118 tables (C1 through C23 with various sub-tables)
- VIP: `VIP1`

### Floor 2:
- Format: `H1`, `H1-1`, `H1-2`, `B1`, `B1-1`, etc.
- Pattern: H series (bow section) and B series (middle section)
- Total: 160+ tables (H1 through H17, B1 through B26)
- VIP: `VIP2`, `VIP3`

### Floor 3:
- Format: `A1`, `A1-1`, `A1-2`, `A1-3`, `A1-4`, `A1-5`, etc.
- Pattern: A series with varying sub-table counts
- Total: 200+ tables (A1 through A36 with various sub-tables)

## Setup Instructions

### 1. Populate the Database

**Option A: Initial Setup (All Floors)**
```
http://localhost/SawasdeeBackend/floorplan/populate_tables_from_svg.php
```

**Option B: Update Floor 1 Only (Recommended)**
```
http://localhost/SawasdeeBackend/floorplan/update_floor1_tables.php
```

The Floor 1 update script will:
- Clear existing Floor 1 table data only
- Insert new Floor 1 tables with C1, C1-1, C1-2... naming scheme
- Preserve VIP rooms and other floor data
- Insert 118 tables total for Floor 1

**Verify the Update:**
```
http://localhost/SawasdeeBackend/floorplan/verify_floor1_tables.php
```

### 2. Test the System

Use the test page to verify the dynamic system is working:

```
http://localhost/SawasdeeBackend/floorplan/test_dynamic_tables.php
```

This page provides:
- Floor switching functionality
- Table selection testing
- Debug information
- Clear visual feedback

### 3. Integration with Main System

The dynamic system is automatically loaded in `mainpage.php` through the included JavaScript file.

## JavaScript API

### DynamicTableRenderer Class

The main class that handles table rendering:

```javascript
// Access the global instance
window.dynamicTableRenderer

// Get selected tables
const selected = window.dynamicTableRenderer.getSelectedTables();

// Clear selection
window.dynamicTableRenderer.clearSelectedTables();

// Set selected tables
window.dynamicTableRenderer.setSelectedTables(['A1/1', 'A1/2']);

// Reload tables for current floor
window.dynamicTableRenderer.loadTablesForCurrentFloor();
```

### Events

The system triggers custom events:

```javascript
// Listen for table selection changes
$(document).on('tableSelectionChanged', function(event, data) {
    console.log('Selected tables:', data.selectedTables);
    console.log('Clicked table:', data.tableId);
});
```

## Table States and Colors

Tables are colored based on their current state:

- **Blue (#539bff)**: Available table (default)
- **Red (#dc3545)**: Booked table
- **Green (#28a745)**: Selected table
- **Gray (#6c757d)**: Disabled table

## Disabled Table Logic

The system checks the `status` and `disabled_until` fields from the `kp_tables` table to determine if a table should be disabled:

### Conditions:

1. **Normal Display**: `status = 'active'` AND `disabled_until` is blank/null
   - Table displays normally and is clickable

2. **Disabled (Date Match)**: `status = 'disabled'` AND `disabled_until` matches current date
   - Table is grayed out with `.table-disabled` class
   - Not clickable, shows tooltip with disable reason

3. **Disabled (Permanent)**: `status = 'disabled'` AND `disabled_until` is blank/null
   - Table is grayed out with `.table-disabled` class
   - Not clickable, shows "disabled" tooltip

4. **Disabled (Future Date)**: `status = 'disabled'` AND `disabled_until` is in the future
   - Table is grayed out until the specified date
   - Not clickable, shows tooltip with disable date

### CSS Classes:

- `.table-disabled`: Applied to disabled tables
  - `opacity: 0.5`
  - `cursor: not-allowed`
  - Gray fill color (#6c757d)
  - No hover effects

## API Endpoints

### get_tables.php

Fetches table data from the database:

**Parameters:**
- `floor` (string): Floor number (1, 2, or 3)
- `row` (string): Filter by row (optional, default: 'all')
- `status` (string): Filter by status (optional, default: 'all')

**Response:**
```json
[
  {
    "id": "A1/1",
    "row": "A1",
    "status": "active",
    "x": 100,
    "y": 200,
    "width": 50,
    "height": 30,
    "disabled_until": null
  }
]
```

## API Endpoints (Updated)

### get_disabled_tables.php

Fetches disabled table data from the database based on a specific date:

**Parameters:**
- `date` (string, optional): Date in YYYY-MM-DD format from myDate input field. Defaults to current date if not provided.

**Example Usage:**
```
get_disabled_tables.php?date=2025-01-23
```

**Response:**
```json
{
  "status": "success",
  "selected_date": "2025-01-23",
  "count": 2,
  "tables": [
    {
      "id": "C1",
      "status": "disabled",
      "disabled_until": "2025-01-25",
      "selected_date": "2025-01-23"
    },
    {
      "id": "C2",
      "status": "disabled",
      "disabled_until": null,
      "selected_date": "2025-01-23"
    }
  ]
}
```

**Logic:**
- Returns tables with `status = 'disabled'`
- Includes tables where `disabled_until` is NULL (permanently disabled)
- Includes tables where `disabled_until >= selected_date`
- Includes tables where `disabled_until = selected_date` (disabled on that specific date)

## Integration with Existing Systems

The dynamic table system integrates with:

1. **Booking System**: Uses existing `get_booked_tables.php` for booking status
2. **Disabled Tables**: Uses `get_disabled_tables.php` with date parameter for disabled status
3. **Table Setup**: Works with the table setup system for managing table positions
4. **VIP Rooms**: Preserves existing VIP room functionality

## Troubleshooting

### No Tables Displayed

1. Check if database is populated: Run `populate_tables_from_svg.php`
2. Check browser console for JavaScript errors
3. Verify database connection in `get_tables.php`

### Tables Not Clickable

1. Ensure event listeners are properly attached
2. Check for JavaScript errors in console
3. Verify SVG elements have correct classes and IDs

### Wrong Floor Data

1. Check floor detection logic in `get_tables.php`
2. Verify table ID formats match expected patterns
3. Check floor parameter being passed to API

## Future Enhancements

Potential improvements for the system:

1. **Visual Table Editor**: Drag-and-drop interface for positioning tables
2. **Bulk Operations**: Select and modify multiple tables at once
3. **Table Templates**: Predefined layouts for different floor configurations
4. **Real-time Updates**: WebSocket integration for live table status updates
5. **Mobile Optimization**: Touch-friendly interface for mobile devices

## Support

For issues or questions about the dynamic table system, check:

1. Browser console for JavaScript errors
2. PHP error logs for server-side issues
3. Database logs for SQL-related problems
4. Test page for isolated testing of functionality
