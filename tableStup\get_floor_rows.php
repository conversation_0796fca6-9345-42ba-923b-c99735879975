<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Get floor parameter
    $floor = isset($_GET['floor']) ? $_GET['floor'] : '1';

    // Define rows for each floor
    $floorRows = [];
    
    if ($floor === '1') {
        $floorRows = [
            // ['value' => 'VIP', 'label' => 'VIP Section', 'description' => 'VIP tables (VIP1)'],
            ['value' => 'C1', 'label' => 'Row C1', 'description' => 'Lower Section – Cozy Indoor Area, 4 tables'],
            ['value' => 'C2', 'label' => 'Row C2', 'description' => 'Lower Section – Cozy Indoor Area, 6 tables'],
            ['value' => 'C3', 'label' => 'Row C3', 'description' => 'Lower Section – Cozy Indoor Area, 6 tables'],
            ['value' => 'C4', 'label' => 'Row C4', 'description' => 'Lower Section – Cozy Indoor Area, 6 tables'],
            ['value' => 'C5', 'label' => 'Row C5', 'description' => 'Lower Section – Cozy Indoor Area, 5 tables'],
            ['value' => 'C6', 'label' => 'Row C6', 'description' => 'Lower Section – Cozy Indoor Area, 5 tables'],
            ['value' => 'C7', 'label' => 'Row C7', 'description' => 'Lower Section – Cozy Indoor Area, 5 tables'],
            ['value' => 'C8', 'label' => 'Row C8', 'description' => 'Lower Section – Cozy Indoor Area, 5 tables'],
            ['value' => 'C9', 'label' => 'Row C9', 'description' => 'Lower Section – Cozy Indoor Area, 6 tables'],
            ['value' => 'C10', 'label' => 'Row C10', 'description' => 'Lower Section – Cozy Indoor Area, 5 tables'],
            ['value' => 'C11', 'label' => 'Row C11', 'description' => 'Lower Section – Cozy Indoor Area, 6 tables'],
            ['value' => 'C12', 'label' => 'Row C12', 'description' => 'Lower Section – Cozy Indoor Area, 4 tables'],
            ['value' => 'C14', 'label' => 'Row C14', 'description' => 'Lower Section – Cozy Indoor Area, 6 tables'],
            ['value' => 'C15', 'label' => 'Row C15', 'description' => 'Lower Section – Cozy Indoor Area, 6 tables'],
            ['value' => 'C16', 'label' => 'Row C16', 'description' => 'Lower Section – Cozy Indoor Area, 6 tables'],
            ['value' => 'C17', 'label' => 'Row C17', 'description' => 'Lower Section – Cozy Indoor Area, 5 tables'],
            ['value' => 'C18', 'label' => 'Row C18', 'description' => 'Lower Section – Cozy Indoor Area, 5 tables'],
            ['value' => 'C19', 'label' => 'Row C19', 'description' => 'Lower Section – Cozy Indoor Area, 5 tables'],
            ['value' => 'C20', 'label' => 'Row C20', 'description' => 'Lower Section – Cozy Indoor Area, 5 tables'],
            ['value' => 'C21', 'label' => 'Row C21', 'description' => 'Lower Section – Cozy Indoor Area, 6 tables'],
            ['value' => 'C22', 'label' => 'Row C22', 'description' => 'Lower Section – Cozy Indoor Area, 5 tables'],
            ['value' => 'C23', 'label' => 'Row C23', 'description' => 'Lower Section – Cozy Indoor Area, 6 tables']
        ];
    } elseif ($floor === '2') {
        $floorRows = [
            // H section (bow of ship)
            // ['value' => 'VIP', 'label' => 'VIP Section', 'description' => 'VIP tables (VIP2, VIP3)'],
            ['value' => 'H1', 'label' => 'Row H1', 'description' => 'Bow Section – Front View / Panorama Zone, 3 tables'],
            ['value' => 'H2', 'label' => 'Row H2', 'description' => 'Bow Section – Front View / Panorama Zone, 4 tables'],
            ['value' => 'H3', 'label' => 'Row H3', 'description' => 'Bow Section – Front View / Panorama Zone, 4 tables'],
            ['value' => 'H4', 'label' => 'Row H4', 'description' => 'Bow Section – Front View / Panorama Zone, 4 tables'],
            ['value' => 'H5', 'label' => 'Row H5', 'description' => 'Bow Section – Front View / Panorama Zone, 4 tables'],
            ['value' => 'H6', 'label' => 'Row H6', 'description' => 'Bow Section – Front View / Panorama Zone, 4 tables'],
            ['value' => 'H7', 'label' => 'Row H7', 'description' => 'Bow Section – Front View / Panorama Zone, 3 tables'],
            ['value' => 'H8', 'label' => 'Row H8', 'description' => 'Bow Section – Front View / Panorama Zone, 4 tables'],
            ['value' => 'H9', 'label' => 'Row H9', 'description' => 'Bow Section – Front View / Panorama Zone, 4 tables'],
            ['value' => 'H10', 'label' => 'Row H10', 'description' => 'Bow Section – Front View / Panorama Zone, 4 tables'],
            ['value' => 'H11', 'label' => 'Row H11', 'description' => 'Bow Section – Front View / Panorama Zone, 4 tables'],
            ['value' => 'H12', 'label' => 'Row H12', 'description' => 'Bow Section – Front View / Panorama Zone, 4 tables'],
            ['value' => 'H14', 'label' => 'Row H14', 'description' => 'Bow Section – Front View / Panorama Zone, 1 table'],
            ['value' => 'H15', 'label' => 'Row H15', 'description' => 'Bow Section – Front View / Panorama Zone, 2 tables'],
            ['value' => 'H16', 'label' => 'Row H16', 'description' => 'Bow Section – Front View / Panorama Zone, 5 tables'],
            ['value' => 'H17', 'label' => 'Row H17', 'description' => 'Bow Section – Front View / Panorama Zone, 6 tables'],
            // B section (middle part of cruise)
            ['value' => 'B1', 'label' => 'Row B1', 'description' => 'Middle Section – Dining Area, 4 tables'],
            ['value' => 'B2', 'label' => 'Row B2', 'description' => 'Middle Section – Dining Area, 4 tables'],
            ['value' => 'B3', 'label' => 'Row B3', 'description' => 'Middle Section – Dining Area, 4 tables'],
            ['value' => 'B4', 'label' => 'Row B4', 'description' => 'Middle Section – Dining Area, 4 tables'],
            ['value' => 'B5', 'label' => 'Row B5', 'description' => 'Middle Section – Dining Area, 5 tables'],
            ['value' => 'B6', 'label' => 'Row B6', 'description' => 'Middle Section – Dining Area, 5 tables'],
            ['value' => 'B7', 'label' => 'Row B7', 'description' => 'Middle Section – Dining Area, 4 tables'],
            ['value' => 'B8', 'label' => 'Row B8', 'description' => 'Middle Section – Dining Area, 5 tables'],
            ['value' => 'B9', 'label' => 'Row B9', 'description' => 'Middle Section – Dining Area, 4 tables'],
            ['value' => 'B10', 'label' => 'Row B10', 'description' => 'Middle Section – Dining Area, 5 tables'],
            ['value' => 'B11', 'label' => 'Row B11', 'description' => 'Middle Section – Dining Area, 5 tables'],
            ['value' => 'B12', 'label' => 'Row B12', 'description' => 'Middle Section – Dining Area, 4 tables'],
            ['value' => 'B14', 'label' => 'Row B14', 'description' => 'Middle Section – Dining Area, 4 tables'],
            ['value' => 'B15', 'label' => 'Row B15', 'description' => 'Middle Section – Dining Area, 4 tables'],
            ['value' => 'B16', 'label' => 'Row B16', 'description' => 'Middle Section – Dining Area, 4 tables'],
            ['value' => 'B17', 'label' => 'Row B17', 'description' => 'Middle Section – Dining Area, 5 tables'],
            ['value' => 'B18', 'label' => 'Row B18', 'description' => 'Middle Section – Dining Area, 5 tables'],
            ['value' => 'B19', 'label' => 'Row B19', 'description' => 'Middle Section – Dining Area, 4 tables'],
            ['value' => 'B20', 'label' => 'Row B20', 'description' => 'Middle Section – Dining Area, 5 tables'],
            ['value' => 'B21', 'label' => 'Row B21', 'description' => 'Middle Section – Dining Area, 4 tables'],
            ['value' => 'B22', 'label' => 'Row B22', 'description' => 'Middle Section – Dining Area, 5 tables'],
            ['value' => 'B23', 'label' => 'Row B23', 'description' => 'Middle Section – Dining Area, 5 tables'],
            ['value' => 'B24', 'label' => 'Row B24', 'description' => 'Middle Section – Dining Area, 2 tables'],
            ['value' => 'B25', 'label' => 'Row B25', 'description' => 'Middle Section – Dining Area, 1 table'],
            ['value' => 'B26', 'label' => 'Row B26', 'description' => 'Middle Section – Dining Area, 1 table']
        ];
    } elseif ($floor === '3') {
        $floorRows = [
            ['value' => 'A1', 'label' => 'Row A1', 'description' => 'Upper Section – Open-Air Deck / Sky View, 6 tables'],
            ['value' => 'A2', 'label' => 'Row A2', 'description' => 'Upper Section – Open-Air Deck / Sky View, 7 tables'],
            ['value' => 'A3', 'label' => 'Row A3', 'description' => 'Upper Section – Open-Air Deck / Sky View, 7 tables'],
            ['value' => 'A4', 'label' => 'Row A4', 'description' => 'Upper Section – Open-Air Deck / Sky View, 7 tables'],
            ['value' => 'A5', 'label' => 'Row A5', 'description' => 'Upper Section – Open-Air Deck / Sky View, 7 tables'],
            ['value' => 'A6', 'label' => 'Row A6', 'description' => 'Upper Section – Open-Air Deck / Sky View, 7 tables'],
            ['value' => 'A7', 'label' => 'Row A7', 'description' => 'Upper Section – Open-Air Deck / Sky View, 7 tables'],
            ['value' => 'A8', 'label' => 'Row A8', 'description' => 'Upper Section – Open-Air Deck / Sky View, 7 tables'],
            ['value' => 'A9', 'label' => 'Row A9', 'description' => 'Upper Section – Open-Air Deck / Sky View, 7 tables'],
            ['value' => 'A10', 'label' => 'Row A10', 'description' => 'Upper Section – Open-Air Deck / Sky View, 6 tables'],
            ['value' => 'A11', 'label' => 'Row A11', 'description' => 'Upper Section – Open-Air Deck / Sky View, 6 tables'],
            ['value' => 'A12', 'label' => 'Row A12', 'description' => 'Upper Section – Open-Air Deck / Sky View, 6 tables'],
            ['value' => 'A14', 'label' => 'Row A14', 'description' => 'Upper Section – Open-Air Deck / Sky View, 6 tables'],
            ['value' => 'A15', 'label' => 'Row A15', 'description' => 'Upper Section – Open-Air Deck / Sky View, 5 tables'],
            ['value' => 'A16', 'label' => 'Row A16', 'description' => 'Upper Section – Open-Air Deck / Sky View, 5 tables'],
            ['value' => 'A17', 'label' => 'Row A17', 'description' => 'Upper Section – Open-Air Deck / Sky View, 4 tables'],
            ['value' => 'A18', 'label' => 'Row A18', 'description' => 'Upper Section – Open-Air Deck / Sky View, 3 tables'],
            ['value' => 'A19', 'label' => 'Row A19', 'description' => 'Upper Section – Open-Air Deck / Sky View, 4 tables'],
            ['value' => 'A20', 'label' => 'Row A20', 'description' => 'Upper Section – Open-Air Deck / Sky View, 6 tables'],
            ['value' => 'A21', 'label' => 'Row A21', 'description' => 'Upper Section – Open-Air Deck / Sky View, 6 tables'],
            ['value' => 'A22', 'label' => 'Row A22', 'description' => 'Upper Section – Open-Air Deck / Sky View, 6 tables'],
            ['value' => 'A23', 'label' => 'Row A23', 'description' => 'Upper Section – Open-Air Deck / Sky View, 6 tables'],
            ['value' => 'A24', 'label' => 'Row A24', 'description' => 'Upper Section – Open-Air Deck / Sky View, 6 tables'],
            ['value' => 'A25', 'label' => 'Row A25', 'description' => 'Upper Section – Open-Air Deck / Sky View, 6 tables'],
            ['value' => 'A26', 'label' => 'Row A26', 'description' => 'Upper Section – Open-Air Deck / Sky View, 6 tables'],
            ['value' => 'A27', 'label' => 'Row A27', 'description' => 'Upper Section – Open-Air Deck / Sky View, 6 tables'],
            ['value' => 'A28', 'label' => 'Row A28', 'description' => 'Upper Section – Open-Air Deck / Sky View, 6 tables'],
            ['value' => 'A29', 'label' => 'Row A29', 'description' => 'Upper Section – Open-Air Deck / Sky View, 6 tables'],
            ['value' => 'A30', 'label' => 'Row A30', 'description' => 'Upper Section – Open-Air Deck / Sky View, 6 tables'],
            ['value' => 'A31', 'label' => 'Row A31', 'description' => 'Upper Section – Open-Air Deck / Sky View, 6 tables'],
            ['value' => 'A32', 'label' => 'Row A32', 'description' => 'Upper Section – Open-Air Deck / Sky View, 6 tables'],
            ['value' => 'A33', 'label' => 'Row A33', 'description' => 'Upper Section – Open-Air Deck / Sky View, 5 tables'],
            ['value' => 'A34', 'label' => 'Row A34', 'description' => 'Upper Section – Open-Air Deck / Sky View, 4 tables'],
            ['value' => 'A35', 'label' => 'Row A35', 'description' => 'Upper Section – Open-Air Deck / Sky View, 3 tables'],
            ['value' => 'A36', 'label' => 'Row A36', 'description' => 'Upper Section – Open-Air Deck / Sky View, 3 tables']
        ];
    }

    // Get floor information
    $floorInfo = [];
    if ($floor === '1') {
        $floorInfo = [
            'name' => 'Floor 1 - Lower Section',
            'description' => 'Lower Section area with VIP section and C-rows (C1-C23), total 118 tables',
            'total_rows' => 23,
            'total_tables' => 118,
            'sections' => ['VIP Section (VIP1-VIP2)', 'Lower Section (C1-C23)']
        ];
    } elseif ($floor === '2') {
        $floorInfo = [
            'name' => 'Floor 2 - Middle Deck',
            'description' => 'Middle deck with bow section (H1-H17) and middle section (B1-B26), total 160 tables',
            'total_rows' => 39,
            'total_tables' => 160,
            'sections' => ['Bow Section (H1-H17)', 'Middle Section (B1-B26)']
        ];
    } elseif ($floor === '3') {
        $floorInfo = [
            'name' => 'Floor 3 - Upper Section',
            'description' => 'Upper Section with A-rows (A1-A36), total 200 tables',
            'total_rows' => 35,
            'total_tables' => 200,
            'sections' => ['Upper Section (A1-A36)']
        ];
    }

    // Return data
    echo json_encode([
        'floor' => $floor,
        'floor_info' => $floorInfo,
        'rows' => $floorRows
    ]);

} catch (Exception $e) {
    echo json_encode(['error' => $e->getMessage()]);
}
?>
