<?php
/**
 * <PERSON><PERSON><PERSON> to completely rebuild kp_tables database with new table structure
 * Clears all existing data and adds new tables for all floors
 */

session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

include '../dbconnect/_dbconnect.php';

try {
    $conn = db_connect();
    
    echo "<h2>Rebuilding Complete kp_tables Database</h2>";
    echo "<p><strong>⚠️ This will delete ALL existing table data and rebuild from scratch</strong></p>";
    
    // Clear ALL existing data
    echo "<h3>Step 1: Clearing all existing table data...</h3>";
    $clearSql = "DELETE FROM kp_tables";
    $clearStmt = $conn->prepare($clearSql);
    $clearStmt->execute();
    echo "<p style='color: orange;'>✓ Cleared all existing table data</p>";
    
    // Floor 1 Tables
    echo "<h3>Step 2: Adding Floor 1 Tables...</h3>";
    $floor1Tables = [
        'VIP1', 'C1', 'C1-1', 'C1-2', 'C1-3', 'C2', 'C2-1', 'C2-2', 'C2-3', 'C2-4', 'C2-5', 
        'C3', 'C3-1', 'C3-2', 'C3-3', 'C3-4', 'C3-5', 'C4', 'C4-1', 'C4-2', 'C4-3', 'C4-4', 'C4-5', 
        'C5', 'C5-1', 'C5-2', 'C5-3', 'C5-4', 'C6', 'C6-1', 'C6-2', 'C6-3', 'C6-4', 
        'C7', 'C7-1', 'C7-2', 'C7-3', 'C7-4', 'C8', 'C8-1', 'C8-2', 'C8-3', 'C8-4', 
        'C9', 'C9-1', 'C9-2', 'C9-3', 'C9-4', 'C9-5', 'C10', 'C10-1', 'C10-2', 'C10-3', 'C10-4', 
        'C11', 'C11-1', 'C11-2', 'C11-3', 'C11-4', 'C11-5', 'C12', 'C12-1', 'C12-2', 'C12-3', 
        'C14', 'C14-1', 'C14-2', 'C14-3', 'C14-4', 'C14-5', 'C15', 'C15-1', 'C15-2', 'C15-3', 'C15-4', 'C15-5', 
        'C16', 'C16-1', 'C16-2', 'C16-3', 'C16-4', 'C16-5', 'C17', 'C17-1', 'C17-2', 'C17-3', 'C17-4', 
        'C18', 'C18-1', 'C18-2', 'C18-3', 'C18-4', 'C19', 'C19-1', 'C19-2', 'C19-3', 'C19-4', 
        'C20', 'C20-1', 'C20-2', 'C20-3', 'C20-4', 'C21', 'C21-1', 'C21-2', 'C21-3', 'C21-4', 'C21-5', 
        'C22', 'C22-1', 'C22-2', 'C22-3', 'C22-4', 'C23', 'C23-1', 'C23-2', 'C23-3', 'C23-4', 'C23-5'
    ];
    
    // Floor 2 Tables
    echo "<h3>Step 3: Adding Floor 2 Tables...</h3>";
    $floor2Tables = [
        'VIP2', 'VIP3', 'H1', 'H1-1', 'H1-2', 'H2', 'H2-1', 'H2-2', 'H2-3', 'H3', 'H3-1', 'H3-2', 'H3-3', 
        'H4', 'H4-1', 'H4-2', 'H4-3', 'H5', 'H5-1', 'H5-2', 'H5-3', 'H6', 'H6-1', 'H6-2', 'H6-3', 
        'H7', 'H7-1', 'H7-2', 'H8', 'H8-1', 'H8-2', 'H8-3', 'H9', 'H9-1', 'H9-2', 'H9-3', 
        'H10', 'H10-1', 'H10-2', 'H10-3', 'H11', 'H11-1', 'H11-2', 'H11-3', 'H12', 'H12-1', 'H12-2', 'H12-3', 
        'H14', 'H15', 'H15-1', 'H16', 'H16-1', 'H16-2', 'H16-3', 'H16-4', 'H17', 'H17-1', 'H17-2', 'H17-3', 'H17-4', 'H17-5', 
        'B1', 'B1-1', 'B1-2', 'B1-3', 'B2', 'B2-1', 'B2-2', 'B2-3', 'B3', 'B3-1', 'B3-2', 'B3-3', 
        'B4', 'B4-1', 'B4-2', 'B4-3', 'B5', 'B5-1', 'B5-2', 'B5-3', 'B5-4', 'B6', 'B6-1', 'B6-2', 'B6-3', 'B6-4', 
        'B7', 'B7-1', 'B7-2', 'B7-3', 'B8', 'B8-1', 'B8-2', 'B8-3', 'B8-4', 'B9', 'B9-1', 'B9-2', 'B9-3', 
        'B10', 'B10-1', 'B10-2', 'B10-3', 'B10-4', 'B11', 'B11-1', 'B11-2', 'B11-3', 'B11-4', 'B12', 'B12-1', 'B12-2', 'B12-3', 
        'B14', 'B14-1', 'B14-2', 'B14-3', 'B15', 'B15-1', 'B15-2', 'B15-3', 'B16', 'B16-1', 'B16-2', 'B16-3', 
        'B17', 'B17-1', 'B17-2', 'B17-3', 'B17-4', 'B18', 'B18-1', 'B18-2', 'B18-3', 'B18-4', 'B19', 'B19-1', 'B19-2', 'B19-3', 
        'B20', 'B20-1', 'B20-2', 'B20-3', 'B20-4', 'B21', 'B21-1', 'B21-2', 'B21-3', 'B22', 'B22-1', 'B22-2', 'B22-3', 'B22-4', 
        'B23', 'B23-1', 'B23-2', 'B23-3', 'B23-4', 'B24', 'B24-1', 'B25', 'B26'
    ];
    
    // Prepare insert statement
    $insertSql = "INSERT INTO kp_tables (table_id, x_position, y_position, width, height, status) VALUES (?, ?, ?, ?, ?, 'active')";
    $insertStmt = $conn->prepare($insertSql);
    
    // Insert Floor 1 tables
    $floor1Count = 0;
    $currentX = 100;
    $currentY = 50;
    $spacingX = 55;
    $spacingY = 35;
    $tablesPerRow = 12;
    
    foreach ($floor1Tables as $index => $tableName) {
        // Special positioning for VIP1
        if ($tableName === 'VIP1') {
            $x = 50;
            $y = 250;
            $width = 110;
            $height = 150;
        } else {
            // Calculate position for regular tables
            $adjustedIndex = $index - 1; // Subtract 1 for VIP1
            $rowIndex = intval($adjustedIndex / $tablesPerRow);
            $colIndex = $adjustedIndex % $tablesPerRow;
            
            $x = $currentX + ($colIndex * $spacingX);
            $y = $currentY + ($rowIndex * $spacingY);
            
            // Main tables (without dash) are slightly larger
            if (strpos($tableName, '-') === false) {
                $width = 60;
                $height = 35;
            } else {
                $width = 50;
                $height = 30;
            }
        }
        
        $insertStmt->execute([$tableName, $x, $y, $width, $height]);
        $floor1Count++;
    }
    
    echo "<p style='color: green;'>✓ Added {$floor1Count} Floor 1 tables</p>";
    
    // Insert Floor 2 tables
    $floor2Count = 0;
    $currentX = 100;
    $currentY = 50;
    
    foreach ($floor2Tables as $index => $tableName) {
        // Special positioning for VIP rooms
        if ($tableName === 'VIP2') {
            $x = 50;
            $y = 50;
            $width = 110;
            $height = 150;
        } elseif ($tableName === 'VIP3') {
            $x = 200;
            $y = 50;
            $width = 110;
            $height = 150;
        } else {
            // Calculate position for regular tables
            $adjustedIndex = $index - 2; // Subtract 2 for VIP2 and VIP3
            $rowIndex = intval($adjustedIndex / $tablesPerRow);
            $colIndex = $adjustedIndex % $tablesPerRow;
            
            $x = $currentX + ($colIndex * $spacingX);
            $y = $currentY + ($rowIndex * $spacingY) + 200; // Offset for VIP rooms
            
            // Main tables (without dash) are slightly larger
            if (strpos($tableName, '-') === false) {
                $width = 60;
                $height = 35;
            } else {
                $width = 50;
                $height = 30;
            }
        }
        
        $insertStmt->execute([$tableName, $x, $y, $width, $height]);
        $floor2Count++;
    }
    
    echo "<p style='color: green;'>✓ Added {$floor2Count} Floor 2 tables</p>";
    
    echo "<p><strong>Floor 1 Total:</strong> {$floor1Count} tables</p>";
    echo "<p><strong>Floor 2 Total:</strong> {$floor2Count} tables</p>";
    echo "<p style='color: blue;'>ℹ️ Floor 3 will be added in the next step due to size limits...</p>";
    
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='rebuild_all_tables_part2.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>→ Continue with Floor 3</a>";
    echo "<a href='verify_all_tables.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>→ Verify Tables</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error</h3>";
    echo "<p style='color: red;'>An error occurred: " . $e->getMessage() . "</p>";
}
?>
