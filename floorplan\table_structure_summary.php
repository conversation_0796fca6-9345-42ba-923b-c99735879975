<?php
/**
 * Complete Table Structure Summary
 * Shows the final table structure for all floors
 */

session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

include '../dbconnect/_dbconnect.php';

try {
    $conn = db_connect();
    
    echo "<h2>Complete Table Structure Summary</h2>";
    echo "<p>This page shows the final table structure for all floors in the kp_tables database.</p>";
    
    // Get all tables grouped by floor
    $sql = "SELECT table_id FROM kp_tables ORDER BY 
            CASE 
                WHEN table_id = 'VIP1' THEN 1
                WHEN table_id LIKE 'C%' THEN 1
                WHEN table_id = 'VIP2' THEN 2
                WHEN table_id = 'VIP3' THEN 2
                WHEN table_id LIKE 'H%' THEN 2
                WHEN table_id LIKE 'B%' THEN 2
                WHEN table_id LIKE 'A%' THEN 3
                ELSE 4
            END,
            table_id";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    
    $allTables = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $allTables[] = $row['table_id'];
    }
    
    // Separate tables by floor
    $floor1Tables = array_filter($allTables, function($table) {
        return $table === 'VIP1' || strpos($table, 'C') === 0;
    });
    
    $floor2Tables = array_filter($allTables, function($table) {
        return $table === 'VIP2' || $table === 'VIP3' || strpos($table, 'H') === 0 || strpos($table, 'B') === 0;
    });
    
    $floor3Tables = array_filter($allTables, function($table) {
        return strpos($table, 'A') === 0;
    });
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; margin: 20px 0;'>";
    
    // Floor 1 Summary
    echo "<div style='background: #e3f2fd; border: 1px solid #2196f3; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #1976d2;'>Floor 1 - " . count($floor1Tables) . " Tables</h3>";
    echo "<p><strong>Pattern:</strong> VIP1, C1, C1-1, C1-2, C1-3, C2, C2-1, etc.</p>";
    echo "<div style='max-height: 200px; overflow-y: auto; background: white; padding: 10px; border-radius: 3px; font-family: monospace; font-size: 12px;'>";
    
    $floor1Groups = [];
    foreach ($floor1Tables as $table) {
        if ($table === 'VIP1') {
            $floor1Groups['VIP'][] = $table;
        } else {
            $mainTable = strpos($table, '-') !== false ? explode('-', $table)[0] : $table;
            $floor1Groups[$mainTable][] = $table;
        }
    }
    
    foreach ($floor1Groups as $group => $tables) {
        echo "<strong>{$group}:</strong> " . implode(', ', $tables) . "<br>";
    }
    echo "</div>";
    echo "</div>";
    
    // Floor 2 Summary
    echo "<div style='background: #f3e5f5; border: 1px solid #9c27b0; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #7b1fa2;'>Floor 2 - " . count($floor2Tables) . " Tables</h3>";
    echo "<p><strong>Pattern:</strong> VIP2, VIP3, H1, H1-1, H1-2, B1, B1-1, etc.</p>";
    echo "<div style='max-height: 200px; overflow-y: auto; background: white; padding: 10px; border-radius: 3px; font-family: monospace; font-size: 12px;'>";
    
    $floor2Groups = [];
    foreach ($floor2Tables as $table) {
        if (strpos($table, 'VIP') === 0) {
            $floor2Groups['VIP'][] = $table;
        } else {
            $mainTable = strpos($table, '-') !== false ? explode('-', $table)[0] : $table;
            $floor2Groups[$mainTable][] = $table;
        }
    }
    
    foreach ($floor2Groups as $group => $tables) {
        echo "<strong>{$group}:</strong> " . implode(', ', $tables) . "<br>";
    }
    echo "</div>";
    echo "</div>";
    
    // Floor 3 Summary
    echo "<div style='background: #e8f5e8; border: 1px solid #4caf50; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #388e3c;'>Floor 3 - " . count($floor3Tables) . " Tables</h3>";
    echo "<p><strong>Pattern:</strong> A1, A1-1, A1-2, A1-3, A1-4, A1-5, A2, A2-1, etc.</p>";
    echo "<div style='max-height: 200px; overflow-y: auto; background: white; padding: 10px; border-radius: 3px; font-family: monospace; font-size: 12px;'>";
    
    $floor3Groups = [];
    foreach ($floor3Tables as $table) {
        $mainTable = strpos($table, '-') !== false ? explode('-', $table)[0] : $table;
        $floor3Groups[$mainTable][] = $table;
    }
    
    foreach ($floor3Groups as $group => $tables) {
        echo "<strong>{$group}:</strong> " . implode(', ', $tables) . "<br>";
    }
    echo "</div>";
    echo "</div>";
    
    echo "</div>";
    
    // Overall Statistics
    echo "<div style='background: #fff3e0; border: 1px solid #ff9800; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #f57c00;'>Overall Statistics</h3>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>";
    
    echo "<div style='text-align: center;'>";
    echo "<div style='font-size: 2em; font-weight: bold; color: #1976d2;'>" . count($floor1Tables) . "</div>";
    echo "<div>Floor 1 Tables</div>";
    echo "</div>";
    
    echo "<div style='text-align: center;'>";
    echo "<div style='font-size: 2em; font-weight: bold; color: #7b1fa2;'>" . count($floor2Tables) . "</div>";
    echo "<div>Floor 2 Tables</div>";
    echo "</div>";
    
    echo "<div style='text-align: center;'>";
    echo "<div style='font-size: 2em; font-weight: bold; color: #388e3c;'>" . count($floor3Tables) . "</div>";
    echo "<div>Floor 3 Tables</div>";
    echo "</div>";
    
    echo "<div style='text-align: center;'>";
    echo "<div style='font-size: 2em; font-weight: bold; color: #f57c00;'>" . count($allTables) . "</div>";
    echo "<div>Total Tables</div>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    
    // API Test Results
    echo "<div style='background: #f5f5f5; border: 1px solid #ccc; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>API Test Results</h3>";
    echo "<div style='display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;'>";
    
    for ($floor = 1; $floor <= 3; $floor++) {
        $apiUrl = "get_tables.php?floor={$floor}&status=all";
        $apiData = @file_get_contents("http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/" . $apiUrl);
        $apiTables = json_decode($apiData, true);
        
        echo "<div style='background: white; padding: 15px; border-radius: 3px;'>";
        echo "<h4>Floor {$floor} API</h4>";
        
        if ($apiTables && !isset($apiTables['error'])) {
            echo "<div style='color: #28a745;'>✅ Success</div>";
            echo "<div>Returned: " . count($apiTables) . " tables</div>";
        } else {
            echo "<div style='color: #dc3545;'>❌ Error</div>";
            echo "<div>Issue with API response</div>";
        }
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
    
    // Navigation
    echo "<div style='background: #e9ecef; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Navigation & Testing</h3>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;'>";
    
    $links = [
        ['test_dynamic_tables.php', 'Test Dynamic Tables', '#007bff'],
        ['admin_dynamic_tables.php', 'Admin Panel', '#17a2b8'],
        ['verify_all_tables.php', 'Verify Tables', '#28a745'],
        ['mainpage.php', 'Main Floorplan', '#6c757d']
    ];
    
    foreach ($links as $link) {
        echo "<a href='{$link[0]}' style='background: {$link[2]}; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; text-align: center; display: block;'>{$link[1]}</a>";
    }
    
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error</h3>";
    echo "<p style='color: red;'>An error occurred: " . $e->getMessage() . "</p>";
}
?>
