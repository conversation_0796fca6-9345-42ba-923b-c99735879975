<?php
/**
 * <PERSON><PERSON><PERSON> to update Floor 1 tables in the database
 * This script will add the new Floor 1 table configuration
 */

session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

include '../dbconnect/_dbconnect.php';

try {
    // Connect to database
    $conn = db_connect();
    
    // Define the new Floor 1 tables
    $floor1Tables = [
        'VIP1', 'VIP2',
        'C1', 'C1-1', 'C1-2', 'C1-3',
        'C2', 'C2-1', 'C2-2', 'C2-3', 'C2-4', 'C2-5',
        'C3', 'C3-1', 'C3-2', 'C3-3', 'C3-4', 'C3-5',
        'C4', 'C4-1', 'C4-2', 'C4-3', 'C4-4', 'C4-5',
        'C5', 'C5-1', 'C5-2', 'C5-3', 'C5-4',
        'C6', 'C6-1', 'C6-2', 'C6-3', 'C6-4',
        'C7', 'C7-1', 'C7-2', 'C7-3', 'C7-4',
        'C8', 'C8-1', 'C8-2', 'C8-3', 'C8-4',
        'C9', 'C9-1', 'C9-2', 'C9-3', 'C9-4', 'C9-5',
        'C10', 'C10-1', 'C10-2', 'C10-3', 'C10-4',
        'C11', 'C11-1', 'C11-2', 'C11-3', 'C11-4', 'C11-5',
        'C12', 'C12-1', 'C12-2', 'C12-3',
        'C14', 'C14-1', 'C14-2', 'C14-3', 'C14-4', 'C14-5',
        'C15', 'C15-1', 'C15-2', 'C15-3', 'C15-4', 'C15-5',
        'C16', 'C16-1', 'C16-2', 'C16-3', 'C16-4', 'C16-5',
        'C17', 'C17-1', 'C17-2', 'C17-3', 'C17-4',
        'C18', 'C18-1', 'C18-2', 'C18-3', 'C18-4',
        'C19', 'C19-1', 'C19-2', 'C19-3', 'C19-4',
        'C20', 'C20-1', 'C20-2', 'C20-3', 'C20-4',
        'C21', 'C21-1', 'C21-2', 'C21-3', 'C21-4', 'C21-5',
        'C22', 'C22-1', 'C22-2', 'C22-3', 'C22-4',
        'C23', 'C23-1', 'C23-2', 'C23-3', 'C23-4', 'C23-5'
    ];
    
    echo "<h2>Updating Floor 1 Tables</h2>";
    echo "<p>Total tables to process: " . count($floor1Tables) . "</p>";
    
    // First, remove old Floor 1 tables (A1-A8 format)
    echo "<h3>Removing old Floor 1 tables...</h3>";
    $deleteOldSql = "DELETE FROM kp_tables WHERE table_id LIKE 'A1/%' OR table_id LIKE 'A2/%' OR table_id LIKE 'A3/%' OR table_id LIKE 'A4/%' OR table_id LIKE 'A5/%' OR table_id LIKE 'A6/%' OR table_id LIKE 'A7/%' OR table_id LIKE 'A8/%'";
    $deleteStmt = $conn->prepare($deleteOldSql);
    $deleteStmt->execute();
    $deletedCount = $deleteStmt->rowCount();
    echo "<p>Removed $deletedCount old Floor 1 tables.</p>";
    
    // Insert new Floor 1 tables
    echo "<h3>Adding new Floor 1 tables...</h3>";
    $insertSql = "INSERT IGNORE INTO kp_tables (table_id, status, x_position, y_position, width, height, disabled_until) VALUES (?, 'active', 0, 0, 50, 30, NULL)";
    $insertStmt = $conn->prepare($insertSql);
    
    $addedCount = 0;
    $skippedCount = 0;
    
    foreach ($floor1Tables as $index => $tableId) {
        try {
            $insertStmt->execute([$tableId]);
            if ($insertStmt->rowCount() > 0) {
                $addedCount++;
                echo "<span style='color: green;'>✓</span> Added table: $tableId<br>";
            } else {
                $skippedCount++;
                echo "<span style='color: orange;'>⚠</span> Skipped (already exists): $tableId<br>";
            }
        } catch (Exception $e) {
            echo "<span style='color: red;'>✗</span> Error adding table $tableId: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<h3>Summary</h3>";
    echo "<ul>";
    echo "<li>Tables added: <strong>$addedCount</strong></li>";
    echo "<li>Tables skipped (already existed): <strong>$skippedCount</strong></li>";
    echo "<li>Old tables removed: <strong>$deletedCount</strong></li>";
    echo "</ul>";
    
    // Verify the update
    echo "<h3>Verification</h3>";
    $verifySql = "SELECT COUNT(*) as count FROM kp_tables WHERE table_id IN ('" . implode("','", $floor1Tables) . "')";
    $verifyStmt = $conn->prepare($verifySql);
    $verifyStmt->execute();
    $verifyResult = $verifyStmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p>Current Floor 1 tables in database: <strong>" . $verifyResult['count'] . "</strong></p>";
    
    if ($verifyResult['count'] == count($floor1Tables)) {
        echo "<p style='color: green; font-weight: bold;'>✅ All Floor 1 tables have been successfully updated!</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ Some tables may be missing. Please check the database.</p>";
    }
    
    echo "<hr>";
    echo "<p><a href='index.php'>← Back to Table Setup</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error</h3>";
    echo "<p>An error occurred: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.php'>← Back to Table Setup</a></p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Floor 1 Tables Update</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h2, h3 { color: #333; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <!-- Content is generated above -->
</body>
</html>
