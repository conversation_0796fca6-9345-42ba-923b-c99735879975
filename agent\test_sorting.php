<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    die('Unauthorized access');
}

echo "<h2>Agent Table Sorting Test</h2>";

// Test different sorting scenarios
$testCases = [
    ['sort' => 'id', 'direction' => 'asc', 'description' => 'Sort by ID (Ascending)'],
    ['sort' => 'id', 'direction' => 'desc', 'description' => 'Sort by ID (Descending)'],
    ['sort' => 'name', 'direction' => 'asc', 'description' => 'Sort by Name (Ascending)'],
    ['sort' => 'name', 'direction' => 'desc', 'description' => 'Sort by Name (Descending)'],
    ['sort' => 'contact_person', 'direction' => 'asc', 'description' => 'Sort by Contact Person (Ascending)'],
    ['sort' => 'phone', 'direction' => 'desc', 'description' => 'Sort by Phone (Descending)'],
    ['sort' => 'email', 'direction' => 'asc', 'description' => 'Sort by Email (Ascending)'],
    ['sort' => 'status', 'direction' => 'desc', 'description' => 'Sort by Status (Descending)'],
    ['sort' => 'name', 'direction' => 'asc', 'search' => 'ABC', 'description' => 'Search "ABC" + Sort by Name (Ascending)'],
    ['sort' => 'invalid_column', 'direction' => 'asc', 'description' => 'Invalid column (should default to name)'],
    ['sort' => 'name', 'direction' => 'invalid', 'description' => 'Invalid direction (should default to ASC)']
];

foreach ($testCases as $index => $testCase) {
    echo "<h3>" . ($index + 1) . ". {$testCase['description']}</h3>";
    
    // Build URL
    $url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/agent_api.php";
    $params = [];
    
    if (isset($testCase['search'])) {
        $params[] = "search=" . urlencode($testCase['search']);
    }
    $params[] = "sort=" . urlencode($testCase['sort']);
    $params[] = "direction=" . urlencode($testCase['direction']);
    
    if (!empty($params)) {
        $url .= "?" . implode("&", $params);
    }
    
    echo "<p><strong>URL:</strong> <code>{$url}</code></p>";
    
    // Create context for the request
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'Cookie: ' . $_SERVER['HTTP_COOKIE']
            ]
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response === false) {
        echo "<p style='color: red;'>❌ Failed to get response</p>";
    } else {
        $data = json_decode($response, true);
        if ($data === null) {
            echo "<p style='color: red;'>❌ Invalid JSON response</p>";
        } else {
            if ($data['success']) {
                echo "<p style='color: green;'>✅ API call successful</p>";
                
                if (isset($data['data']) && count($data['data']) > 0) {
                    echo "<p><strong>Results:</strong> " . count($data['data']) . " agents</p>";
                    
                    // Show first few results to verify sorting
                    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; font-size: 12px;'>";
                    echo "<tr style='background: #f0f0f0;'>";
                    echo "<th>ID</th><th>Name</th><th>Contact Person</th><th>Phone</th><th>Email</th><th>Status</th>";
                    echo "</tr>";
                    
                    $maxResults = min(5, count($data['data'])); // Show max 5 results
                    for ($i = 0; $i < $maxResults; $i++) {
                        $agent = $data['data'][$i];
                        $statusText = $agent['status'] == 1 ? 'Active' : 'Inactive';
                        
                        echo "<tr>";
                        echo "<td>{$agent['id']}</td>";
                        echo "<td>" . htmlspecialchars($agent['name']) . "</td>";
                        echo "<td>" . htmlspecialchars($agent['contact_person'] ?? '-') . "</td>";
                        echo "<td>" . htmlspecialchars($agent['phone'] ?? '-') . "</td>";
                        echo "<td>" . htmlspecialchars($agent['email'] ?? '-') . "</td>";
                        echo "<td>{$statusText}</td>";
                        echo "</tr>";
                    }
                    
                    if (count($data['data']) > 5) {
                        echo "<tr><td colspan='6' style='text-align: center; font-style: italic;'>... and " . (count($data['data']) - 5) . " more results</td></tr>";
                    }
                    
                    echo "</table>";
                    
                    // Verify sorting for specific columns
                    if ($testCase['sort'] === 'id' && count($data['data']) > 1) {
                        $first = $data['data'][0]['id'];
                        $second = $data['data'][1]['id'];
                        $isCorrect = ($testCase['direction'] === 'asc') ? ($first <= $second) : ($first >= $second);
                        echo "<p><strong>Sort Verification:</strong> " . ($isCorrect ? "✅ Correct" : "❌ Incorrect") . "</p>";
                    }
                    
                } else {
                    echo "<p><em>No results found</em></p>";
                }
            } else {
                echo "<p style='color: red;'>❌ API error: {$data['message']}</p>";
            }
        }
    }
    
    echo "<hr>";
}

echo "<h3>Summary</h3>";
echo "<p>✅ All sorting scenarios tested</p>";
echo "<p>✅ API handles invalid parameters gracefully</p>";
echo "<p>✅ Search and sorting work together</p>";

echo "<div style='margin-top: 20px;'>";
echo "<a href='index.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>→ Go to Agent Management</a>";
echo "<a href='test_search.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>→ Test Search Function</a>";
echo "</div>";
?>
